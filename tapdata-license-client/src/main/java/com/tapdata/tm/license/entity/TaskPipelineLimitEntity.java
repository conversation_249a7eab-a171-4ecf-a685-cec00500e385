package com.tapdata.tm.license.entity;

import com.tapdata.tm.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@Document("TaskPipelineLimit")
public class TaskPipelineLimitEntity extends BaseEntity {
	/**
	 * 通道编号：md5(join("|", sort(source.instanceId, target.instanceId)))
	 */
	private String pipelineId;
	/**
	 * 数据源实例信息（{source.instanceId, source.instanceTag, source.pdkId, source.pdkHash}, {target.instanceId, target.instanceTag, target.pdkId, target.pdkHash}）
	 */
	private List<Map<String, String>> instanceInfos;
	/**
	 * 引用的任务编号
	 */
	private List<String> taskIds;
}

package com.tapdata.tm.license.service;

import com.tapdata.tm.utils.RC4Util;
import io.tapdata.entity.schema.value.DateTime;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class test {
	public static void main(String[] args) {
		String sql = "CREATE TABLE Orders (" +
			"  OrderID INT NOT NULL," +
			"  OrderQuantity INT NOT NULL," +
			"  OrderPrice DECIMAL(18,2)," +
			"  OrderDate DATETIME DEFAULT CURRENT_TIMESTAMP()," +
			"  created_at DATETIME DEFAULT sysdate()" +
			");";

		// 正则表达式，用于匹配 DEFAULT 及其后面的函数
		String regex = "DEFAULT\\s+[^(,\\s]+(\\(\\))?";

		// 创建一个Pattern对象
		Pattern pattern = Pattern.compile(regex);

		// 创建一个Matcher对象
		Matcher matcher = pattern.matcher(sql);

		// 使用正则表达式替换匹配的部分
		String filteredSQL = matcher.replaceAll("");

		System.out.println("Filtered SQL: ");
		System.out.println(filteredSQL);
	}
}

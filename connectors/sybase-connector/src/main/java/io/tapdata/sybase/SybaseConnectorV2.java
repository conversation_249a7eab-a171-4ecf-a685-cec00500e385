package io.tapdata.sybase;

import com.sybase.jdbc42.tds.SybTimestamp;
import io.tapdata.common.CommonDbConnector;
import io.tapdata.common.SqlExecuteCommandFunction;
import io.tapdata.common.dml.NormalRecordWriter;
import io.tapdata.entity.codec.TapCodecsRegistry;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.schema.*;
import io.tapdata.entity.schema.value.*;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.DbKit;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.kit.StringKit;
import io.tapdata.pdk.apis.annotations.TapConnectorClass;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.context.TapConnectionContext;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import io.tapdata.pdk.apis.entity.*;
import io.tapdata.pdk.apis.functions.ConnectorFunctions;
import io.tapdata.pdk.apis.functions.PDKMethod;
import io.tapdata.pdk.apis.functions.connection.RetryOptions;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.sybase.cdc.SybaseDataTypeConvert;
import io.tapdata.sybase.cdc.SybaseLogMiner;
import io.tapdata.sybase.cdc.SybaseOffset;
import io.tapdata.sybase.dml.SybaseRecordWriter;
import io.tapdata.sybase.dml.SybaseWriteRecorder;
import io.tapdata.sybase.extend.SybaseColumn;
import io.tapdata.sybase.extend.SybaseConfig;
import io.tapdata.sybase.extend.SybaseConnectionTest;
import io.tapdata.sybase.extend.SybaseContext;
import io.tapdata.sybase.util.ConnectorUtil;
import io.tapdata.sybase.util.Utils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.tapdata.sybase.cdc.SybaseSqlConstant.*;

@TapConnectorClass("spec.json")
public class SybaseConnectorV2 extends CommonDbConnector {
    public static final int CDC_PROCESS_FAIL_EXCEPTION_CODE = 555005;
    private SybaseContext sybaseContext;
    private String encode;
    private String decode;
    private String outCode;
    private boolean needEncode = false;
    private SybaseConfig sybaseConfig;
    private SybaseLogMiner logMiner;
    private long identityCache = 100;

    @Override
    public void onStart(TapConnectionContext tapConnectionContext) {
        sybaseConfig = new SybaseConfig().load(tapConnectionContext.getConnectionConfig());
        sybaseContext = new SybaseContext(sybaseConfig);
        commonDbConfig = sybaseConfig;
        jdbcContext = sybaseContext;
        isConnectorStarted(tapConnectionContext, connectorContext -> {
            firstConnectorId = (String) connectorContext.getStateMap().get("firstConnectorId");
            if (EmptyKit.isNull(firstConnectorId)) {
                firstConnectorId = UUID.randomUUID().toString().replace("-", "");
                connectorContext.getStateMap().put("firstConnectorId", firstConnectorId);
            }
            sybaseConfig.load(tapConnectionContext.getNodeConfig());
        });
        commonSqlMaker = new SybaseSqlMaker();
        if (Boolean.TRUE.equals(sybaseConfig.getCreateAutoInc())) {
            commonSqlMaker.createAutoInc(true);
        }
        if (Boolean.TRUE.equals(sybaseConfig.getApplyDefault())) {
            commonSqlMaker.applyDefault(true);
        }
        tapLogger = tapConnectionContext.getLog();
        if (sybaseConfig.getCdcPlugin() == null) {
            sybaseConfig.setCdcPlugin(0);
            sybaseConfig.setDebugLog(0);
            sybaseConfig.setAutoEncode(true);
            sybaseConfig.setAutoMinerNormalSleepMs(10000);
            sybaseConfig.setAutoMinerBrokenSleepMs(100);
            sybaseConfig.setAutoMinerRescanBatch(99999);
            sybaseConfig.setManualMinerScanBatch(999);
            sybaseConfig.setManualMinerScanTimeoutMs(25000);
            sybaseConfig.setDumpLogTimeS(900);
        }
        encode = sybaseConfig.getAutoEncode() ? Optional.ofNullable(sybaseConfig.getEncode()).orElse("cp850") : null;
        decode = sybaseConfig.getAutoEncode() ? Optional.ofNullable(sybaseConfig.getDecode()).orElse("big5") : null;
        outCode = sybaseConfig.getAutoEncode() ? Optional.ofNullable(sybaseConfig.getOutDecode()).orElse("utf-8") : null;
    }

    @Override
    public void onStop(TapConnectionContext connectionContext) {
        ErrorKit.ignoreAnyError(() -> {
            if (null != logMiner) {
                logMiner.stopMiner();
            }
        });
        ErrorKit.ignoreAnyError(() -> sybaseContext.execute(String.format(TRUNC_LOG_ON_CHKPT_ENABLE, sybaseConfig.getDatabase())));
        Optional.ofNullable(sybaseContext).ifPresent(c -> {
            try {
                this.sybaseContext.close();
                this.sybaseContext = null;
            } catch (Exception ignore) {
            }
        });
    }

    private void onDestroy(TapConnectorContext connectorContext) throws Throwable {
        if (EmptyKit.isNull(connectorContext.getStateMap().get("canDestroy")) || !(boolean) connectorContext.getStateMap().get("canDestroy")) {
            return;
        }
        try {
            onStart(connectorContext);
            sybaseContext.execute(LTM_IGNORE);
            sybaseContext.execute(String.format(CDC_DUMP_LOG, sybaseConfig.getDatabase()));
        } catch (Exception ignore) {
            connectorContext.getLog().info("cdc dump log error:{}", ignore.getMessage());
        } finally {
            onStop(connectorContext);
        }

        try {
            onStart(connectorContext);
            try (Connection connection = sybaseContext.getConnection();
                 Statement statement = connection.createStatement()) {
                statement.execute(LTM_IGNORE);
                statement.execute(String.format(CDC_DUMP_LOG, sybaseConfig.getDatabase()));
            }
        } catch (Exception ignore) {
            connectorContext.getLog().info("cdc dump log error:{}", ignore.getMessage());
        } finally {
            onStop(connectorContext);
        }
    }

    @Override
    public void registerCapabilities(ConnectorFunctions connectorFunctions, TapCodecsRegistry codecRegistry) {
        codecRegistry.registerFromTapValue(TapMapValue.class, "json", tapValue -> toJson(tapValue.getValue()));
        codecRegistry.registerFromTapValue(TapArrayValue.class, "json", tapValue -> toJson(tapValue.getValue()));
        codecRegistry.registerToTapValue(SybTimestamp.class, (value, tapType) -> {
            String sybTimestamp = value.toString();
            return new TapDateTimeValue(new DateTime(LocalDateTime.parse(sybTimestamp.replace(" ", "T"))));
        });
        codecRegistry.registerFromTapValue(TapTimeValue.class, tapTimeValue -> tapTimeValue.getValue().toTimeStr());
        codecRegistry.registerFromTapValue(TapDateTimeValue.class, tapDateTimeValue -> formatTapDateTimeV2(tapDateTimeValue.getValue(), "yyyy-MM-dd HH:mm:ss.SSSSSS"));
        codecRegistry.registerFromTapValue(TapDateValue.class, tapDateValue -> formatTapDateTimeV2(tapDateValue.getValue(), "yyyy-MM-dd"));
        codecRegistry.registerFromTapValue(TapYearValue.class, "char(4)", tapYearValue -> formatTapDateTime(tapYearValue.getValue(), "yyyy"));
        codecRegistry.registerFromTapValue(TapBooleanValue.class, "bit(1)", TapValue::getValue);
        connectorFunctions
                .supportErrorHandleFunction(this::errorHandle)
                .supportQueryByAdvanceFilter(this::queryByAdvanceFilterWithOffset)
                .supportBatchCount(this::batchCount)
                .supportBatchRead(this::batchReadV2)
                .supportGetTableNamesFunction(this::getTableNames)
                .supportStreamRead(this::streamReadV2)
                .supportTimestampToStreamOffset(this::timestampToStreamOffset)
                .supportExecuteCommandFunction((a, b, c) -> SqlExecuteCommandFunction.executeCommand(a, b, () -> sybaseContext.getConnection(), this::isAlive, c))
                .supportRunRawCommandFunction(this::runRawCommand)
                .supportStreamReadMultiConnectionFunction(this::multiStreamReadV2)
                .supportCreateTableV2(this::createTableV3)
                .supportCreateIndex(this::createIndex)
                .supportQueryIndexes(this::queryIndexes)
                .supportQueryConstraints(this::queryConstraint)
                .supportCreateConstraint(this::createConstraint)
                .supportDropConstraint(this::dropConstraint)
                .supportClearTable(this::clearTable)
                .supportDropTable(this::dropTable)
                .supportWriteRecord(this::writeRecord)
                .supportReleaseExternalFunction(this::onDestroy)
                .supportExportEventSqlFunction(this::exportEventSql);
        //.supportCountRawCommandFunction(this::countRawCommand);
    }

    protected RetryOptions errorHandle(TapConnectionContext tapConnectionContext, PDKMethod pdkMethod, Throwable throwable) {
        RetryOptions retryOptions = RetryOptions.create();
        retryOptions.setNeedRetry(
                (throwable instanceof CoreException && ((CoreException) throwable).getCode() == CDC_PROCESS_FAIL_EXCEPTION_CODE)
                        || (null != matchThrowable(throwable, com.sybase.jdbc42.jdbc.SybConnectionDeadException.class))
        );
        retryOptions.beforeRetryMethod(() -> {
            try {
                synchronized (this) {
                    if (retryOptions.isNeedRetry()) {
                        this.onStop(tapConnectionContext);
                        this.onStart(tapConnectionContext);
                    }
                }
            } catch (Throwable ignore) {
            }
        });
        return retryOptions;
    }

    protected void runRawCommand(TapConnectorContext connectorContext, String command, TapTable tapTable, int eventBatchSize, Consumer<List<TapEvent>> eventsOffsetConsumer) throws Throwable {
        final List<TapEvent>[] tapEvents = new List[]{list()};
        try {
            sybaseContext.query(command, resultSet -> {
                List<String> columnNames = DbKit.getColumnsFromResultSet(resultSet);
                while (isAlive() && resultSet.next()) {
                    DataMap dataMap = DbKit.getRowFromResultSet(resultSet, columnNames);
                    tapEvents[0].add(insertRecordEvent(dataMap, tapTable.getId()));
                    if (tapEvents[0].size() == eventBatchSize) {
                        eventsOffsetConsumer.accept(tapEvents[0]);
                        tapEvents[0] = list();
                    }
                }
            });
        } finally {
            if (!tapEvents[0].isEmpty()) eventsOffsetConsumer.accept(tapEvents[0]);
        }
    }

    /**
     * @deprecated
     */
    private Map<String, Object> filterTimeForMysql(ResultSet resultSet, ResultSetMetaData metaData, Set<String> dateTypeSet, String encode, String decode, String outCode) throws SQLException {
        Map<String, Object> data = new HashMap<>();

        for (int index = 0, keyIndex = 1; index < metaData.getColumnCount(); keyIndex++, index++) {
            String columnName = metaData.getColumnName(keyIndex);
            try {
                Object value;
                String columnTypeName = metaData.getColumnTypeName(keyIndex);
                final String upperColumnType = null == columnTypeName ? "" : columnTypeName.toUpperCase(Locale.ROOT);
                if ("TIME".equals(upperColumnType) || "DATE".equals(upperColumnType)) {
                    value = resultSet.getString(keyIndex);
                } else if (upperColumnType.contains("CHAR")
                        || upperColumnType.contains("TEXT")
                        || upperColumnType.contains("SYSNAME")) {
                    String string = resultSet.getString(keyIndex);
                    value = null == string ? null : Utils.convertString(string, encode, decode);
                } else {
                    value = resultSet.getObject(keyIndex);
                    if (null == value && dateTypeSet.contains(columnName)) {
                        value = resultSet.getString(keyIndex);
                    }
                }
                data.put(columnName, value);
            } catch (Exception e) {
                throw new RuntimeException("Read column value failed, column name: " + columnName + ", data: " + data + "; Error: " + e.getMessage(), e);
            }
        }
        return data;
    }

    @Override
    protected void queryByAdvanceFilterWithOffset(TapConnectorContext connectorContext, TapAdvanceFilter filter, TapTable table, Consumer<FilterResults> consumer) throws Throwable {
        String sql = commonSqlMaker.buildSelectClause(table, filter, false) + getSchemaAndTable(table.getId()) + commonSqlMaker.buildSqlByAdvanceFilter(filter);
        int batchSize = null != filter.getBatchSize() && filter.getBatchSize().compareTo(0) > 0 ? filter.getBatchSize() : BATCH_ADVANCE_READ_LIMIT;
        jdbcContext.query(sql, resultSet -> {
            FilterResults filterResults = new FilterResults();
            //get all column names
            Set<String> dateTypeSet = ConnectorUtil.dateFields(table);
            Map<String, String> typeAndNameFromMetaData = SybaseDataTypeConvert.getTypeAndName(resultSet);
            while (isAlive() && resultSet.next()) {
                filterResults.add(SybaseDataTypeConvert.filterTimeForDataBase(resultSet, typeAndNameFromMetaData, dateTypeSet, needEncode, decode, outCode));
                if (filterResults.getResults().size() == batchSize) {
                    consumer.accept(filterResults);
                    filterResults = new FilterResults();
                }
            }
            //last events those less than eventBatchSize
            if (EmptyKit.isNotEmpty(filterResults.getResults())) {
                consumer.accept(filterResults);
            }
        });
    }

    @Override
    protected void queryByFilter(TapConnectorContext connectorContext, List<TapFilter> filters, TapTable tapTable, Consumer<List<FilterResult>> listConsumer) {
        final boolean needEncode = sybaseConfig.getAutoEncode();
        final String encode = needEncode ? Optional.ofNullable(sybaseConfig.getEncode()).orElse("cp850") : null;
        final String decode = needEncode ? Optional.ofNullable(sybaseConfig.getDecode()).orElse("big5") : null;
        final String outCode = needEncode ? Optional.ofNullable(sybaseConfig.getOutDecode()).orElse("utf-8") : null;
        List<FilterResult> filterResults = new LinkedList<>();
        for (TapFilter filter : filters) {
            String sql = String.format("select * from %s where %s",
                    getSchemaAndTable(tapTable.getId()),
                    commonSqlMaker.buildKeyAndValue(filter.getMatch(), "and", "="));
            FilterResult filterResult = new FilterResult();
            try {
                jdbcContext.query(sql, resultSet -> {
                    Set<String> dateTypeSet = ConnectorUtil.dateFields(tapTable);
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    if (resultSet.next()) {
                        filterResult.setResult(filterTimeForMysql(resultSet, metaData, dateTypeSet, encode, decode, outCode));
                    }
                });
            } catch (Throwable e) {
                filterResult.setError(e);
            } finally {
                filterResults.add(filterResult);
            }
        }
        listConsumer.accept(filterResults);
    }

    protected long batchCount(TapConnectorContext tapConnectorContext, TapTable tapTable) throws Throwable {
        try {
            AtomicLong count = new AtomicLong(0);
            String sql = String.format("select count(*) from %s", getSchemaAndTable(tapTable.getId()));
            try {
                jdbcContext.queryWithNext(sql, resultSet -> count.set(resultSet.getLong(1)));
            } catch (Exception e) {
                sql = "select count(*) from " + tapTable.getId();
                jdbcContext.queryWithNext(sql, resultSet -> count.set(resultSet.getLong(1)));
            }
            return count.get();
        } catch (SQLException e) {
            exceptionCollector.collectReadPrivileges("batchCount", Collections.emptyList(), e);
            throw e;
        }
    }

    private void batchReadV2(TapConnectorContext tapConnectorContext, TapTable tapTable, Object offsetState, int eventBatchSize, BiConsumer<List<TapEvent>, Object> eventsOffsetConsumer) {

        if (null == tapTable) {
            throw new CoreException("Start batch read with an empty tap table, batch read is failed");
        }
        final String tableId = tapTable.getId();
        if (null == tableId || "".equals(tableId.trim())) {
            throw new CoreException("Start batch read with tap table which table id is empty, batch read is failed");
        }

        String columns = tapTable.getNameFieldMap().keySet().stream().map(c -> " \"" + c + "\" ").collect(Collectors.joining(","));
        String sql = String.format("SELECT %s FROM %s", "".equals(columns.trim()) ? "*" : columns, getSchemaAndTable(tableId));
        final Set<String> dateTypeSet = ConnectorUtil.dateFields(tapTable);
        final AtomicReference<List<TapEvent>> tapEvents = new AtomicReference<>(new ArrayList<>());
        try {
            sybaseContext.query(sql, resultSet -> {
                Map<String, String> typeAndNameFromMetaData = SybaseDataTypeConvert.getTypeAndName(resultSet);
                while (resultSet.next()) {
                    tapEvents.get().add(insertRecordEvent(
                            SybaseDataTypeConvert.filterTimeForDataBase(resultSet, typeAndNameFromMetaData, dateTypeSet, needEncode, encode, decode),
                            tableId).referenceTime(System.currentTimeMillis()));
                    if (tapEvents.get().size() == eventBatchSize) {
                        eventsOffsetConsumer.accept(tapEvents.get(), offsetState);
                        tapEvents.set(new ArrayList<>());
                    }
                }
            });
        } catch (Exception e) {
            tapConnectorContext.getLog().error("Batch read failed, table name: {}, sql: {}, error msg: {}", tableId, sql, e.getMessage());
        } finally {
            if (!tapEvents.get().isEmpty()) {
                eventsOffsetConsumer.accept(tapEvents.get(), offsetState);
            }
        }

    }

    private Object timestampToStreamOffset(TapConnectorContext tapConnectorContext, Long startTime) {
        if (EmptyKit.isNotNull(startTime)) {
            tapLogger.warn("TimestampToStreamOffset is not support for sybase connector");
        }
        new SybaseBeforeCdc(sybaseContext, tapConnectorContext.getLog()).validLtm();
        return new SybaseOffset();
    }

    private SybaseOffset buildSybaseOffset() throws SQLException {
        SybaseOffset sybaseOffset = new SybaseOffset();
        sybaseContext.normalQuery("dbcc gettrunc", resultSet -> {
            if (resultSet.next()) {
                sybaseOffset.setStartRid(resultSet.getLong(1));
                sybaseOffset.setRowId(0);
            }
        });
        return sybaseOffset;
    }

    @Override
    public int tableCount(TapConnectionContext connectionContext) throws SQLException {
        AtomicInteger count = new AtomicInteger(0);
        sybaseContext.queryAllTables(null, 999999, (tables) -> {
            if (null != tables && !tables.isEmpty()) count.getAndAdd(tables.size());
        });
        return count.get();
    }

    @Override
    public ConnectionOptions connectionTest(TapConnectionContext connectionContext, Consumer<TestItem> consumer) {
        sybaseConfig = new SybaseConfig().load(connectionContext.getConnectionConfig());
        ConnectionOptions connectionOptions = ConnectionOptions.create();
        connectionOptions.connectionString(sybaseConfig.getConnectionString());
        try (SybaseConnectionTest mysqlConnectionTest = new SybaseConnectionTest(sybaseConfig, consumer)) {
            mysqlConnectionTest.testOneByOne();
        }

        if (sybaseConfig.getPort() <= 0) return connectionOptions;
        if (StringUtils.isAnyBlank(sybaseConfig.getHost(), sybaseConfig.getDatabase(), sybaseConfig.getSchema()))
            return connectionOptions;

        connectionOptions.setInstanceUniqueId(StringKit.md5(String.join("|"
                , sybaseConfig.getHost()
                , String.valueOf(sybaseConfig.getPort())
                , sybaseConfig.getDatabase()
        )));
        List<String> options = new ArrayList<>();
        options.add(sybaseConfig.getSchema());
        connectionOptions.setNamespaces(options);
        return connectionOptions;
    }

    @Override
    public void discoverSchema(TapConnectionContext connectionContext, List<String> tables, int tableSize, Consumer<List<TapTable>> consumer) throws SQLException {
//        identityCache = sybaseContext.getIdentityCache();
        super.discoverSchema(connectionContext, tables, tableSize, consumer);
    }

    @Override
    protected void singleThreadDiscoverSchema(List<DataMap> subList, Consumer<List<TapTable>> consumer) throws SQLException {
        List<TapTable> tapTableList = TapSimplify.list();
        List<String> tables = subList.stream().filter(Objects::nonNull).map(map -> map.getString("tableName")).collect(Collectors.toList());
        List<DataMap> columnList = jdbcContext.queryAllColumns(tables);
        Map<String, List<DataMap>> columnMap = columnList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(map -> map.getString("tableName")));
        List<DataMap> indexList = sybaseContext.queryAllIndexes(new ArrayList<>(tables));
        Map<String, List<DataMap>> indexMap = indexList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(map -> map.getString("tableName")));
        List<DataMap> pkList = sybaseContext.queryAllPks(new ArrayList<>(tables));
        Map<String, String> tableLock = sybaseContext.queryAllLocks(new ArrayList<>(tables));
        Map<String, List<DataMap>> pkMap = pkList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(map -> map.getString("tableName")));
        List<DataMap> fkList = sybaseContext.queryAllForeignKeys(new ArrayList<>(tables));
        columnMap.forEach((table, columns) -> {
            TapTable tapTable = table(table);
            List<String> primaryKeys = new ArrayList<>();
            //3、primary key and table index
            ConnectorUtil.makePrimaryKeyAndIndexV2(tapTable, indexMap.get(table), pkMap.get(table), primaryKeys);
            //4、table columns info
            AtomicInteger keyPos = new AtomicInteger(0);
            // 5. table lock info
            String lock = tableLock.getOrDefault(table, "allpages");
            tapTable.setStorageEngine(lock);

            columns.stream()
                    .filter(col -> null != col
                            && null != col.getString("dataType"))
                    .forEach(col -> {
                        TapField tapField = new SybaseColumn(col).getTapField();
                        tapField.setPos(keyPos.incrementAndGet());
                        tapField.setPrimaryKey(primaryKeys.contains(tapField.getName().trim()));
                        tapField.setPrimaryKeyPos(primaryKeys.indexOf(tapField.getName()) + 1);
                        if (tapField.getAutoInc()) {
                            tapField.setAutoIncCacheValue(identityCache);
                        }
                        tapTable.add(tapField);
                    });
            if (null == tapTable.getNameFieldMap() || tapTable.getNameFieldMap().isEmpty()) {
                tapLogger.warn("Table {} can not fund any primary key", tapTable.getId());
            }
            tapTable.setConstraintList(makeForeignKey(fkList, table));
            tapTableList.add(tapTable);
        });
        syncSchemaSubmit(tapTableList, consumer);
    }

    protected List<TapIndex> discoverIndex(String tableName) {
        List<TapIndex> tapIndexList = TapSimplify.list();
        try {
            List<DataMap> indexList = jdbcContext.queryAllIndexes(Collections.singletonList(tableName));

            Map<String, List<DataMap>> indexMap = indexList.stream()
                    .collect(Collectors.groupingBy(idx -> idx.getString("index_name"), LinkedHashMap::new, Collectors.toList()));
            indexMap.forEach((key, value) -> tapIndexList.add(ConnectorUtil.makeTapIndexV2(key, value)));
        } catch (Exception e) {
            tapLogger.warn("Discover index failed, table name: {}, error msg: {}", tableName, e.getMessage());
        }
        return tapIndexList;
    }

    @Override
    protected String getSchemaAndTable(String tableId) {
        return String.format(" %s.%s.%s ", sybaseConfig.getDatabase(), sybaseConfig.getSchema(), tableId);
    }

    private void streamReadV2(TapConnectorContext tapConnectorContext, List<String> currentTables, Object offset, int batchSize, StreamReadConsumer consumer) throws Throwable {
        SybaseOffset sybaseOffset;
        SybaseOffset sybaseOffsetInSybase;

        sybaseOffsetInSybase = buildSybaseOffset();

        if (EmptyKit.isNull(offset)) {
            sybaseOffset = sybaseOffsetInSybase;
        } else {
            sybaseOffset = (SybaseOffset) offset;
            if ((sybaseOffsetInSybase.getStartRid() > sybaseOffset.getStartRid()) ||
                    (sybaseOffsetInSybase.getStartRid() == sybaseOffset.getStartRid() && sybaseOffsetInSybase.getRowId() > sybaseOffset.getRowId())) {
                tapConnectorContext.getLog().info(sybaseOffsetInSybase);
                tapConnectorContext.getLog().info(sybaseOffset);
                tapConnectorContext.getLog().info("sybase offset in database is: {}, in tapdata is: {}, database is bigger, will use it", sybaseOffsetInSybase.toString(), sybaseOffset.toString());
                sybaseOffset = sybaseOffsetInSybase;
            } else {
                tapConnectorContext.getLog().info("sybase offset in database is: {}, in tapdata is: {}, tapdata is bigger, will use it", sybaseOffsetInSybase.toString(), sybaseOffset.toString());
            }
        }

        tapConnectorContext.getLog().info("we will use offset in database, how ever, this is safe: {}", sybaseOffsetInSybase.toString());
        sybaseOffset = sybaseOffsetInSybase;
        sybaseOffset.setRowId(0);

        KVReadOnlyMap<TapTable> tableMap = tapConnectorContext.getTableMap();
        Map<String, TapTable> tapTableMap = ConnectorUtil.getTapTableMap(tableMap);

        List<ConnectionConfigWithTables> connectionConfigWithTables = new ArrayList<>();
        ConnectionConfigWithTables connectionConfigWithTablesItem = new ConnectionConfigWithTables();
        connectionConfigWithTablesItem.connectionConfig(tapConnectorContext.getConnectionConfig());
        connectionConfigWithTablesItem.setTables(currentTables);
        connectionConfigWithTables.add(connectionConfigWithTablesItem);

        logMiner = new SybaseLogMiner(sybaseContext, firstConnectorId, tapConnectorContext.getLog())
                .cdcAccept(consumer,
                        tapTableMap,
                        ConnectorUtil.getBlockFieldsMap(1, tapTableMap, sybaseConfig.getDatabase(), sybaseConfig.getSchema(), currentTables, tapConnectorContext.getLog()),
                        connectionConfigWithTables,
                        unused -> isAlive());
        logMiner.init(
                currentTables,
                tableMap,
                sybaseOffset,
                batchSize,
                consumer
        );
        tapConnectorContext.getStateMap().put("canDestroy", true);
        try {
            if (sybaseConfig.getCdcPlugin() == 0) {
                logMiner.startMiner();
            } else {
                logMiner.startMinerV2();
            }
        } catch (Exception e) {
            for (StackTraceElement stackTraceElement : e.getStackTrace()) {
                tapConnectorContext.getLog().warn(stackTraceElement.toString());
            }
            exceptionCollector.revealException(e);
        }
    }

    private void multiStreamReadV2(TapConnectorContext tapConnectorContext, List<ConnectionConfigWithTables> connectionConfigWithTables, Object offset, int batchSize, StreamReadConsumer consumer) throws Throwable {
        SybaseOffset sybaseOffset;
        if (EmptyKit.isNull(offset)) {
            sybaseOffset = buildSybaseOffset();
        } else {
            sybaseOffset = (SybaseOffset) offset;
        }
        KVReadOnlyMap<TapTable> tableMap = tapConnectorContext.getTableMap();
        Map<String, TapTable> tapTableMap = ConnectorUtil.getTapTableMap(tableMap);
        logMiner = new SybaseLogMiner(sybaseContext, firstConnectorId, tapConnectorContext.getLog())
                .cdcAccept(consumer,
                        tapTableMap,
                        ConnectorUtil.getBlockFieldsMap(1, tapTableMap, connectionConfigWithTables, tapConnectorContext.getLog()),
                        connectionConfigWithTables,
                        unused -> isAlive());
        logMiner.multiInit(
                connectionConfigWithTables,
                tableMap,
                sybaseOffset,
                batchSize,
                consumer
        );
        tapConnectorContext.getStateMap().put("canDestroy", true);
        try {
            if (sybaseConfig.getCdcPlugin() == 0) {
                logMiner.startMiner();
            } else {
                logMiner.startMinerV2();
            }
        } catch (Exception e) {
            for (StackTraceElement stackTraceElement : e.getStackTrace()) {
                tapConnectorContext.getLog().warn(stackTraceElement.toString());
            }
            exceptionCollector.revealException(e);
        }
    }

    protected void writeRecord(TapConnectorContext connectorContext, List<TapRecordEvent> tapRecordEvents, TapTable tapTable, Consumer<WriteListResult<TapRecordEvent>> writeListResultConsumer) throws SQLException {
        List<String> removedColumn = new ArrayList<>();
        if (EmptyKit.isNull(writtenTableMap.get(tapTable.getId()))) {
            List<String> physicalColumn = jdbcContext.queryAllColumns(Collections.singletonList(tapTable.getId())).stream().map(v -> v.getString("columnName")).collect(Collectors.toList());
            if (EmptyKit.isNotEmpty(physicalColumn)) {
                removedColumn.addAll(tapTable.getNameFieldMap().keySet().stream().filter(v -> !physicalColumn.contains(v)).collect(Collectors.toList()));
            }
            writtenTableMap.put(tapTable.getId(), DataMap.create().kv(HAS_REMOVED_COLUMN, removedColumn));
        } else {
            removedColumn.addAll(writtenTableMap.get(tapTable.getId()).getValue(HAS_REMOVED_COLUMN, new ArrayList<>()));
        }
        List<String> autoIncFields = new ArrayList<>();
        if (sybaseConfig.getCreateAutoInc()) {
            if (EmptyKit.isNull(writtenTableMap.get(tapTable.getId()).get(HAS_AUTO_INCR))) {
                List<TapField> fields = tapTable.getNameFieldMap().values().stream().filter(TapField::getAutoInc).collect(Collectors.toList());
                autoIncFields.addAll(fields.stream().map(TapField::getName).collect(Collectors.toList()));
                writtenTableMap.get(tapTable.getId()).put(HAS_AUTO_INCR, autoIncFields);
            } else {
                autoIncFields.addAll(writtenTableMap.get(tapTable.getId()).getValue(HAS_AUTO_INCR, new ArrayList<>()));
            }
        }
        String insertDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_INSERT_POLICY);
        if (insertDmlPolicy == null) {
            insertDmlPolicy = ConnectionOptions.DML_INSERT_POLICY_UPDATE_ON_EXISTS;
        }
        String updateDmlPolicy = connectorContext.getConnectorCapabilities().getCapabilityAlternative(ConnectionOptions.DML_UPDATE_POLICY);
        if (updateDmlPolicy == null) {
            updateDmlPolicy = ConnectionOptions.DML_UPDATE_POLICY_IGNORE_ON_NON_EXISTS;
        }
        NormalRecordWriter sybaseRecordWriter = new SybaseRecordWriter(sybaseContext, tapTable)
                .setInsertPolicy(insertDmlPolicy)
                .setUpdatePolicy(updateDmlPolicy)
                .setTapLogger(connectorContext.getLog())
                .setRemovedColumn(removedColumn);
        if (sybaseConfig.getCreateAutoInc() && EmptyKit.isNotEmpty((List) writtenTableMap.get(tapTable.getId()).get(HAS_AUTO_INCR))) {
            sybaseRecordWriter.closeIdentity();
        }
        sybaseRecordWriter.closeConstraintCheck();
        sybaseRecordWriter.write(tapRecordEvents, writeListResultConsumer, this::isAlive);
    }

    protected void commentOnTable(StringBuilder sb, TapTable tapTable) {

    }

    protected String getCreateConstraintSql(TapTable tapTable, TapConstraint tapConstraint) {
        char escapeChar = commonDbConfig.getEscapeChar();
        StringBuilder sb = new StringBuilder("alter table ");
        sb.append(getSchemaAndTable(tapTable.getId())).append(" add constraint ");
        if (EmptyKit.isNotBlank(tapConstraint.getName())) {
            sb.append(escapeChar).append(tapConstraint.getName()).append(escapeChar);
        } else {
            sb.append(escapeChar).append(DbKit.buildForeignKeyName(tapTable.getId(), tapConstraint, 32)).append(escapeChar);
        }
        sb.append(" foreign key (").append(escapeChar).append(tapConstraint.getMappingFields().stream().map(TapConstraintMapping::getForeignKey).collect(Collectors.joining(escapeChar + "," + escapeChar))).append(escapeChar).append(") references ")
                .append(getSchemaAndTable(tapConstraint.getReferencesTableName())).append('(').append(escapeChar).append(tapConstraint.getMappingFields().stream().map(TapConstraintMapping::getReferenceKey).collect(Collectors.joining(escapeChar + "," + escapeChar))).append(escapeChar).append(')');
        return sb.toString();
    }

    public String exportEventSql(TapConnectorContext connectorContext, TapEvent tapEvent, TapTable table) throws SQLException {
        if(tapEvent instanceof TapInsertRecordEvent){
            SybaseWriteRecorder sybaseWriteRecorder =  new SybaseWriteRecorder(null, table, jdbcContext.getConfig().getSchema());
            return sybaseWriteRecorder.getUpsertSql(((TapInsertRecordEvent)tapEvent).getAfter());
        }
        return null;
    }
}

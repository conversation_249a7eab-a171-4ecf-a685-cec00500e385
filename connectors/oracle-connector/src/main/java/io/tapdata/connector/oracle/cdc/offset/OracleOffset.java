package io.tapdata.connector.oracle.cdc.offset;

import java.io.Serializable;

public class OracleOffset implements Serializable {

    private static final long serialVersionUID = 1451529908242515916L;

    private String sortString;
    private Long offsetValue;
    private Long lastScn;
    private Long pendingScn;
    private Long timestamp;
    private String hexScn;
    private String pendingHexScn;
    private Long pendingTimestamp;
    private int fno;

    public OracleOffset() {

    }

    public OracleOffset(String sortString, Long offsetValue) {
        this.sortString = sortString;
        this.offsetValue = offsetValue;
    }

    public String getSortString() {
        return sortString;
    }

    public void setSortString(String sortString) {
        this.sortString = sortString;
    }

    public Long getOffsetValue() {
        return offsetValue;
    }

    public void setOffsetValue(Long offsetValue) {
        this.offsetValue = offsetValue;
    }

    public Long getLastScn() {
        return lastScn;
    }

    public void setLastScn(Long lastScn) {
        this.lastScn = lastScn;
    }

    public Long getPendingScn() {
        return pendingScn;
    }

    public void setPendingScn(Long pendingScn) {
        this.pendingScn = pendingScn;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getHexScn() {
        return hexScn;
    }

    public void setHexScn(String hexScn) {
        this.hexScn = hexScn;
    }

    public int getFno() {
        return fno;
    }

    public void setFno(int fno) {
        this.fno = fno;
    }

    public Long getPendingTimestamp() {
        return pendingTimestamp;
    }

    public void setPendingTimestamp(Long pendingTimestamp) {
        this.pendingTimestamp = pendingTimestamp;
    }

    public String getPendingHexScn() {
        return pendingHexScn;
    }

    public void setPendingHexScn(String pendingHexScn) {
        this.pendingHexScn = pendingHexScn;
    }
}

package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.common.cdc.LogTransaction;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.util.JdbcUtil;
import io.tapdata.constant.SqlConstant;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

public class RacOracleLogMiner3 extends OracleLogMiner {

    private final int threadSize;
    private final long[] continuousScn;
    private final RedoLogContent[] csfLogContent;
    private final LinkedBlockingQueue<RedoLogContent>[] logQueues;

    public RacOracleLogMiner3(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger, int threadSize) throws SQLException {
        super(oracleJdbcContext, connectorId, tapLogger);
        this.threadSize = threadSize;
        this.continuousScn = new long[threadSize];
        this.csfLogContent = new RedoLogContent[threadSize];
        this.logQueues = new LinkedBlockingQueue[threadSize];
        for (int i = 0; i < threadSize; i++) {
            logQueues[i] = new LinkedBlockingQueue<>(LOG_QUEUE_SIZE / threadSize);
        }
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    protected void readyForInit() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
    }

    @Override
    public void startMiner() throws Throwable {
        long startScn = oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn();
        //挖掘起始scn
        Arrays.fill(continuousScn, startScn);
        tapLogger.info("total start mining scn: " + startScn);
        ExecutorService minerService = new ThreadPoolExecutor(threadSize, threadSize, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
        try {
            for (int i = 0; i < threadSize; i++) {
                int finalI = i;
                minerService.submit(() -> {
                    try (
                            Connection minerConnection = oracleJdbcContext.getConnection();
                            Statement minerStatement = minerConnection.createStatement()
                    ) {
                        setSession(minerStatement);
                        RedoLog logFile = checkArchiveAndOnlineLogWithScn(continuousScn[finalI], 0L, finalI + 1);
                        boolean isOnline = logFile.isOnlineRedo();
                        boolean reload = true;
                        String lastRedoLogContentId = null;
                        long tailScn = 0L;
                        int tailEqualCount = 0;
                        while (isRunning.get()) {
                            boolean allNull = false;
                            if (reload) {
                                String addLogMinerSql = createAddLogMinerSql(logFile);
                                minerStatement.execute(addLogMinerSql);
                                tapLogger.info(ADD_LOG_MINER_SQL_LOG, addLogMinerSql);
                                minerStatement.execute(START_LOG_MINOR_SQL);
                                minerStatement.setFetchSize(1000);
                            }
                            try (ResultSet minerResultSet = minerStatement.executeQuery(analyzeLogSql(continuousScn[finalI] - 1, false))) {
                                Map<String, Object> logData = null;
                                boolean maybeDuplicate = true; //重扫时有可能重复，为了精确一次读
                                while (minerResultSet.next() && isRunning.get()) {
                                    while (ddlStop.get()) {
                                        TapSimplify.sleep(1000);
                                    }
                                    ResultSetMetaData metaData = minerResultSet.getMetaData();
                                    logData = JdbcUtil.buildLogData(
                                            metaData,
                                            minerResultSet,
                                            oracleConfig.getSysZoneId()
                                    );
                                    if (logData.get("SCN") != null && logData.get("SCN") instanceof BigDecimal) {
                                        long scn = ((BigDecimal) logData.get("SCN")).longValue();
                                        long thread = ((BigDecimal) logData.get("THREAD#")).longValue();
                                        if (maybeDuplicate && (scn > continuousScn[finalI] || EmptyKit.isBlank(lastRedoLogContentId))) {
                                            maybeDuplicate = false;
                                        }
                                        continuousScn[finalI] = scn;
                                        if (thread == 0 && ((BigDecimal) logData.get("OPERATION_CODE")).longValue() == 3) {
                                            allNull = true;
                                            break;
                                        }
                                    }
                                    if (maybeDuplicate) {
                                        //直到扫到上次的精确记录为止
                                        if (EmptyKit.isNotBlank(lastRedoLogContentId) && lastRedoLogContentId.equals(generateLogContentId(logData))) {
                                            maybeDuplicate = false;
                                        }
                                        continue;
                                    }
                                    analyzeLog(logData, finalI);
                                }
                                if (allNull) {
                                    tapLogger.warn("【rac miner】all null value, restart at scn: {}", continuousScn[finalI]);
                                    reload = true;
                                    continue;
                                }
                                RedoLogContent tail = new RedoLogContent();
                                if (tailScn == continuousScn[finalI]) {
                                    tailEqualCount++;
                                    if (tailEqualCount > 3) {
                                        oracleJdbcContext.queryWithNext(CHECK_CURRENT_SCN, resultSet -> tail.setScn(resultSet.getLong(1)));
                                        try (ResultSet queryResultSet = minerStatement.executeQuery("select max(scn) from v$logmnr_contents")) {
                                            if (queryResultSet.next() && queryResultSet.getLong(1) <= tail.getScn()) {
                                                tapLogger.info("【rac miner】tail equal count > 3, remark scn: {}", tail.getScn());
                                                enqueueRedoLogContent(tail, finalI);
                                                tailEqualCount = 0;
                                            }
                                        }
                                    }
                                } else {
                                    tailScn = continuousScn[finalI];
                                    tail.setScn(tailScn);
                                    enqueueRedoLogContent(tail, finalI);
                                    tailEqualCount = 0;
                                }
                                lastRedoLogContentId = EmptyKit.isNotNull(logData) ? generateLogContentId(logData) : null;
                                if (isOnline) {
                                    TapSimplify.sleep(oracleConfig.getPollingInterval());
                                    long oldSequence = logFile.getSequence();
                                    while (isRunning.get()) {
                                        logFile = checkArchiveAndOnlineLogWithScn(continuousScn[finalI], oldSequence, finalI + 1);
                                        if (logFile.getSequence() == oldSequence) {
                                            break;
                                        } else {
                                            logFile = checkInactiveOnlineLogWithScn(oldSequence, finalI + 1);
                                            if (EmptyKit.isNotNull(logFile)) {
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    logFile = checkArchiveAndOnlineLogWithScn(continuousScn[finalI], logFile.getSequence() + 1, finalI + 1);
                                }
                                reload = !isOnline || !logFile.isOnlineRedo();
                                isOnline = logFile.isOnlineRedo();
                            } finally {
                                if (reload) {
                                    ErrorKit.ignoreAnyError(() -> minerStatement.execute(END_LOG_MINOR_SQL));
                                }
                            }
                        }
                    } catch (Exception e) {
                        threadException.set(e);
                    }
                });
            }
            while (isRunning.get()) {
                if (EmptyKit.isNotNull(threadException.get())) {
                    throw threadException.get();
                }
                TapSimplify.sleep(1000);
            }
        } finally {
            minerService.shutdown();
        }
    }

    //设置session
    protected void setSession(Statement minerStatement) throws SQLException {
        if (EmptyKit.isNotBlank(oracleConfig.getPdb())) {
            tapLogger.info("database is containerised, switching...");
            minerStatement.execute(SWITCH_TO_CDB_ROOT);
        }
        minerStatement.execute(NLS_DATE_FORMAT);
        minerStatement.execute(NLS_TIMESTAMP_FORMAT);
        minerStatement.execute(NLS_TIMESTAMP_TZ_FORMAT);
        minerStatement.execute(NLS_NUMERIC_FORMAT);
    }

    //在某个rac线程下，通过scn号和最小sequence号查找最合适的最小sequence的日志文件
    private RedoLog checkArchiveAndOnlineLogWithScn(Long startScn, Long startSequence, int thread) {
        RedoLog logFile = null;
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(SINGLE_LOG_FILE_INIT)
        ) {
            preparedStatement.setLong(1, startScn);
            preparedStatement.setLong(2, startSequence);
            preparedStatement.setInt(3, thread);
            while (isRunning.get()) {
                try (
                        ResultSet resultSet = preparedStatement.executeQuery()
                ) {
                    if (resultSet.next()) {
                        logFile = wrapRedoLog(resultSet);
                    }
                }
                TapSimplify.sleep(1000);
                //二次确认
                RedoLog logFileAgain = null;
                try (
                        ResultSet resultSet = preparedStatement.executeQuery()
                ) {
                    if (resultSet.next()) {
                        logFileAgain = wrapRedoLog(resultSet);
                    }
                }
                if (logFile != null && logFile.equals(logFileAgain)) {
                    return logFile;
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return logFile;
    }

    private RedoLog checkInactiveOnlineLogWithScn(Long sequence, int thread) {
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(INACTIVE_LOG_FILE)
        ) {
            preparedStatement.setLong(1, sequence);
            preparedStatement.setInt(2, thread);
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                if (resultSet.next()) {
                    return wrapRedoLog(resultSet);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private RedoLog wrapRedoLog(ResultSet resultSet) throws SQLException {
        RedoLog redoLog = new RedoLog();
        redoLog.setName(resultSet.getString("NAME"));
        redoLog.setFirstChangeScn(resultSet.getLong("FIRST_CHANGE#"));
        //在线的next_change#超过了long的最大值，SQL将next_change#强制为空，代码中为了方便比较next_change#置为Long.MAX_VALUE
        if (EmptyKit.isNull(resultSet.getString("NEXT_CHANGE#"))) {
            redoLog.setNextChangeScn(Long.MAX_VALUE);
        } else {
            redoLog.setNextChangeScn(resultSet.getLong("NEXT_CHANGE#"));
        }
        redoLog.setSequence(resultSet.getLong("SEQUENCE#"));
        redoLog.setOnlineRedo("online".equals(resultSet.getString("STATUS")));
        redoLog.setCreator(resultSet.getString("CREATOR"));
        redoLog.setFirstTime(resultSet.getDate("FIRST_TIME"));
        return redoLog;
    }

    private static final String SINGLE_LOG_FILE_INIT = "select * from\n" +
            "(SELECT 'archived' status, t.NAME, t.thread#, t.sequence#, t.first_change#, t.next_change#, t.creator, t.first_time\n" +
            "from v$archived_log t\n" +
            "where (NAME IS NOT NULL AND DEST_ID != 0 AND STATUS = 'A' AND STANDBY_DEST = 'NO' AND IS_RECOVERY_DEST_FILE = 'NO')\n" +
            "  and next_change# >= ? and sequence# >= ?\n" +
            "union ALL\n" +
            "select 'online', lf.MEMBER NAME, l.thread#, l.sequence#, l.first_change#, null, null, l.first_time\n" +
            "FROM v$log l\n" +
            "         LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP#\n" +
            "where l.status = 'CURRENT' AND lf.IS_RECOVERY_DEST_FILE = 'NO') where thread# = ? order by SEQUENCE#";
    private static final String INACTIVE_LOG_FILE = "select 'archived', lf.MEMBER NAME, l.thread#, l.sequence#, l.first_change#, null, null, l.first_time\n" +
            "FROM v$log l LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP#\n" +
            "where l.status = 'INACTIVE' AND lf.IS_RECOVERY_DEST_FILE = 'NO' AND l.SEQUENCE# = ? AND l.THREAD# = ?";
    private static final String ADD_LOG_MINER_SQL_LOG = "【rac miner】add log miner sql: {}";
    private static final String ADD_LOG_FILE = " SYS.dbms_logmnr.add_logfile(logfilename=>'";

    private String createAddLogMinerSql(RedoLog redoLog) {
        StringBuilder sb = new StringBuilder();
        if (EmptyKit.isNotNull(redoLog)) {
            sb.append("BEGIN");
            sb.append(ADD_LOG_FILE).append(redoLog.getName()).append("',options=>SYS.dbms_logmnr.NEW);");
            sb.append("END;");
        }
        return sb.toString();
    }

    protected void analyzeLog(Object logData, int thread) throws SQLException {
        RedoLogContent redoLogContent = wrapRedoLogContent(logData);
        String operation = redoLogContent.getOperation();
        switch (operation) {
            case SqlConstant.REDO_LOG_OPERATION_SELECT_FOR_UPDATE:
            case SqlConstant.REDO_LOG_OPERATION_LOB_TRIM:
            case SqlConstant.REDO_LOG_OPERATION_LOB_WRITE:
            case SqlConstant.REDO_LOG_OPERATION_UNSUPPORTED:
                return;
            case SqlConstant.REDO_LOG_OPERATION_ROLLBACK:
                if (logData instanceof Map) {
                    if ("0000000000000000".equals(((Map) logData).get("PXID"))) {
                        return;
                    }
                } else {
                    if ("0000000000000000".equals(((ResultSet) logData).getString("PXID"))) {
                        return;
                    }
                }
        }
        if (!validateRedoLogContent(redoLogContent)) {
            return;
        }
        if (csfRedoLogProcess(logData, redoLogContent, thread)) {
            return;
        }
        lastEventTimestamp = redoLogContent.getTimestamp().getTime();
        enqueueRedoLogContent(redoLogContent, thread);
    }

    protected boolean csfRedoLogProcess(Object logData, RedoLogContent redoLogContent, int thread) {
        // handle continuation redo/undo sql
        if (isCsf(logData)) {
            if (csfLogContent[thread] == null) {
                csfLogContent[thread] = new RedoLogContent();
                beanUtils.copyProperties(redoLogContent, csfLogContent[thread]);
            }
            return true;
        } else {
            csfLogContent[thread] = null;
        }
        return false;
    }

    protected void enqueueRedoLogContent(RedoLogContent redoLogContent, int thread) {
        try {
            while (isRunning.get() && !logQueues[thread].offer(redoLogContent, 1, TimeUnit.SECONDS)) {
            }
        } catch (InterruptedException ignore) {
        }
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                RedoLogContent redoLogContent = null;
                int commitCount = 0;
                long lastTimestamp = System.currentTimeMillis();
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        redoLogContent = pollRedoLogContent(logQueues);
                        if (redoLogContent == null || EmptyKit.isNull(redoLogContent.getOperation())) {
                            continue;
                        }
                        tapLogger.debug("redo log content: {}", redoLogContent);
                        //commit事件过多导致延迟增加，每100个非追踪commit事件才会放行一次心跳
                        if (redoLogContent.getOperationCode() == 7 && !transactionBucket.containsKey(redoLogContent.getXid())) {
                            commitCount++;
                            if (commitCount > 100 || (System.currentTimeMillis() - lastTimestamp >= 1000 * 3)) {
                                commitCount = 0;
                                lastTimestamp = System.currentTimeMillis();
                            } else {
                                continue;
                            }
                        }
                    } catch (Exception e) {
                        threadException.set(e);
                    }
                    try {
                        // parse sql
                        if (canParse(redoLogContent)) {
                            RedoLogContent.OperationEnum originOperation = RedoLogContent.OperationEnum.fromOperationCode(redoLogContent.getOperationCode());
                            RedoLogContent.OperationEnum operationEnum = originOperation;
                            if (operationEnum == RedoLogContent.OperationEnum.DELETE) {
                                operationEnum = RedoLogContent.OperationEnum.INSERT;
                                try {
                                    redoLogContent.setRedoRecord(sqlParser.parseSQL(redoLogContent.getSqlUndo(), operationEnum));
                                } catch (Exception e) {
                                    threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                }
                            } else {
                                try {
                                    redoLogContent.setRedoRecord(sqlParser.parseSQL(redoLogContent.getSqlRedo(), operationEnum));
                                } catch (Exception e) {
                                    threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                }
                                if (oracleConfig.getEnableUniqueUpdate() && StringUtils.isNotBlank(redoLogContent.getSqlUndo()) && operationEnum == RedoLogContent.OperationEnum.UPDATE) {
                                    try {
                                        redoLogContent.setUndoRecord(sqlParser.parseSQL(redoLogContent.getSqlUndo(), operationEnum));
                                    } catch (Exception e) {
                                        threadException.set(new RuntimeException("parse failed, redoLogContent: " + redoLogContent, e));
                                    }
                                }
                            }
                            convertStringToObject(redoLogContent);
//                        String mongodbBefore = context.getSettingService().getString("mongodb.before");
//                        if (
//                                redoLogParser.needParseUndo(redoLogContent.getOperation(),
//                                        redoLogContent.getSqlUndo(),
//                                        mongodbBefore,
//                                        context.getJobTargetConn() != null && context.getJobTargetConn().isSupportUpdatePk(),
//                                        context.getJob().getNoPrimaryKey()
//                                )
//                        ) {
//                            redoLogContent.setUndoRecord(redoLogParser.parseSQL(redoLogContent.getSqlUndo(), RedoLogContent.OperationEnum.UPDATE));
//                        }
                        }
                        // process and callback
                        processOrBuffRedoLogContent(redoLogContent, this::sendTransaction);

                    } catch (Throwable e) {
                        threadException.set(e);
                        consumer.streamReadEnded();
                    }
                }
            });
            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String xid = iterator.next();
                            LogTransaction transaction = transactionBucket.get(xid);
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < oracleConfig.getTransactionAliveMinutes() * 60 * 1000L) {
                                break;
                            } else {
                                tapLogger.warn("Uncommitted transaction {} with {} events will be dropped", xid, transaction.getSize());
                                transaction.clearRedoLogContents();
                                iterator.remove();
                            }
                        }
                        int sleep = 60;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    private RedoLogContent pollRedoLogContent(LinkedBlockingQueue<RedoLogContent>[] logQueues) {
        int minIndex = 0;
        long minScn = Long.MAX_VALUE;
        for (int i = 0; i < threadSize; i++) {
            RedoLogContent redoLogContent = logQueues[i].peek();
            if (EmptyKit.isNull(redoLogContent)) {
                TapSimplify.sleep(500);
                return null;
            }
            minScn = Math.min(minScn, redoLogContent.getScn());
            if (redoLogContent.getScn() == minScn) {
                minIndex = i;
            }
        }
        return logQueues[minIndex].poll();
    }
}

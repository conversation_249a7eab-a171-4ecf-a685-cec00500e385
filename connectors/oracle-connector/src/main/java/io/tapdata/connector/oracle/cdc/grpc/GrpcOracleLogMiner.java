package io.tapdata.connector.oracle.cdc.grpc;

import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.NegotiationType;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.tapdata.common.cdc.ILogMiner;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner;
import io.tapdata.connector.oracle.config.OracleConfig;
import io.tapdata.data.*;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

public class GrpcOracleLogMiner extends OracleLogMiner implements ILogMiner {

    private ManagedChannel channel;
    private OracleRedoLogServerGrpc.OracleRedoLogServerBlockingStub blockingStub;
    private RedologRequest redologRequest;
    private TaskHandleRequest taskHandleRequest;
    private static final MessageHeader MESSAGE_HEADER = MessageHeader.newBuilder().setProtocolVersion(1).build();

    public GrpcOracleLogMiner(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws SQLException {
        super(oracleJdbcContext, connectorId, tapLogger);
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        OracleConfig oracleConfig = (OracleConfig) oracleJdbcContext.getConfig();
        if (EmptyKit.isBlank(oracleConfig.getRawLogServerHost())) {
            throw new IllegalArgumentException("rawLogServerString is empty");
        }
        channel = NettyChannelBuilder.forAddress(oracleConfig.getRawLogServerHost(), oracleConfig.getRawLogServerPort())
                .maxInboundMessageSize(Integer.MAX_VALUE)
                .negotiationType(NegotiationType.PLAINTEXT).build();
        blockingStub = OracleRedoLogServerGrpc.newBlockingStub(channel);
        taskHandleRequest = TaskHandleRequest.newBuilder().setHeader(MESSAGE_HEADER).setId(connectorId).build();
        ReaderSource.Builder sourceBuilder = ReaderSource.newBuilder().setSchema(oracleConfig.getSchema())
                .setStandby(oracleConfig.getStandBy())
                .setSyncWaitTimeMS(5000).setType(ReaderType.ONLINE).addSourceDBConnection(SourceConnection.newBuilder()
                        .setHost(oracleConfig.getHost())
                        .setPort(oracleConfig.getPort())
                        .setName(EmptyKit.isBlank(oracleConfig.getDatabase()) ? oracleConfig.getSid() : oracleConfig.getDatabase())
                        .setUsername(oracleConfig.getUser())
                        .setPassword(oracleConfig.getPassword()).build());
        for (String tableName : tableList) {
            sourceBuilder.addSourceTable(SourceTable.newBuilder().setName(tableName).build());
        }
        redologRequest = RedologRequest.newBuilder()
                .setHeader(MESSAGE_HEADER)
                .setId(connectorId)
                .setScn(EmptyKit.isNull(oracleOffset.getLastScn()) ? 0 : oracleOffset.getLastScn())
                .setSource(sourceBuilder.build())
                .setTarget(WriterTarget.newBuilder().setType(WriterType.GRPC).build())
                .build();
        makeTimestampMap();
    }

    private void makeTimestampMap() {
        timestampMap.clear();
        tableList.forEach(t -> {
            TapTable tapTable = tableMap.get(t);
            tapTable.getNameFieldMap().forEach((key, value) -> {
                if (value.getDataType().toLowerCase().startsWith("timestamp")) {
                    timestampMap.put(t + "." + key, getFraction(value.getDataType()));
                }
            });
        });
    }

    private int getFraction(String dataType) {
        dataType = dataType.toLowerCase();
        if (dataType.contains("(")) {
            return Integer.parseInt(dataType.substring(dataType.indexOf("(") + 1, dataType.indexOf(")")));
        }
        return 6;
    }

    protected void ddlFlush() {
        super.ddlFlush();
        makeTimestampMap();
    }

    @Override
    public void startMiner() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
        ControlResponse response = blockingStub.createRedologTask(redologRequest);
        if (response.getCode() == ResponseCode.ALREADY_CREATE) {
            if (ResponseCode.OK == blockingStub.deleteRedologTask(taskHandleRequest).getCode()) {
                response = blockingStub.createRedologTask(redologRequest);
            }
        }
        if (response.getCode() != ResponseCode.OK) {
            throw new IllegalArgumentException(response.getMsg());
        }
        PingResponse pingResponse = blockingStub.ping(PingRequest.getDefaultInstance());
        if (!"ok".equals(pingResponse.getMsg())) {
            throw new IllegalArgumentException("Grpc Log Miner service is not available");
        }
        Iterator<PullRedoLogResponse> logResponseIterator = blockingStub.pullRedoLog(taskHandleRequest);
        try {
            while (logResponseIterator.hasNext()) {
                if (EmptyKit.isNotNull(threadException.get())) {
                    throw new RuntimeException(threadException.get());
                }
                PullRedoLogResponse logResponse = logResponseIterator.next();
                while (ddlStop.get()) {
                    TapSimplify.sleep(1000);
                }
                List<RedoLogContent> logContentList = getRedoLogContentList(logResponse);
                logContentList.forEach(this::enqueueRedoLogContent);
            }
        } catch (Exception e) {
            tapLogger.warn("Grpc error: {}", e);
        }
        if (isRunning.get()) {
            throw new RuntimeException("Exception occurs in Grpc Log Miner service");
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        ControlResponse response = blockingStub.deleteRedologTask(taskHandleRequest);
        if (response.getCode() != ResponseCode.OK) {
            throw new IllegalArgumentException(response.getMsg());
        }
        if (!channel.isShutdown()) {
            channel.shutdown();
        }
    }

    private Map<String, Object> buildLogData(PullRedoLogResponse response) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("scn", response.getScn());
        logData.put("timeStamp", response.getTimeStamp());
        logData.put("xid", response.getXid());

        List<Map<String, Object>> payloadList = new ArrayList<>();
        for (RedologPayload payload : response.getPayloadList()) {
            Map<String, Object> param = new HashMap<>();
            param.put("op", payload.getOp().name());
            Schema schema = payload.getSchema();
            param.put("schema", new HashMap<String, Object>() {{
                put("name", schema.getName());
                put("owner", schema.getOwner());
            }});
            param.put("rid", payload.getRid());
            param.put("ddl", payload.getDdl());
            param.put("seq", payload.getSeq());
            param.put("offset", payload.getOffset());
            param.put("redo", payload.getRedo());
            param.put("num", payload.getNum());

            payloadList.add(param);
        }
        logData.put("payload", payloadList);

        return logData;
    }

    private List<RedoLogContent> getRedoLogContentList(PullRedoLogResponse logResponse) {
        List<RedoLogContent> resultList = new ArrayList<>();
        for (RedologPayload payload : logResponse.getPayloadList()) {
            RedoLogContent redoLogContent = new RedoLogContent();
            redoLogContent.setGrpc(true);
            //固定值
            redoLogContent.setStatus(1L);
            redoLogContent.setScn(logResponse.getScn());
            redoLogContent.setXid(logResponse.getXid());
            redoLogContent.setTimestamp(new Timestamp(logResponse.getTimeStamp()));

            RedologOp op = payload.getOp();
            RedoLogContent.OperationEnum operationEnum = RedoLogContent.OperationEnum.fromRedologOperationCode(op.getNumber());
            redoLogContent.setOperation(operationEnum.getOperation());
            redoLogContent.setOperationCode(operationEnum.getCode());
            redoLogContent.setRsId(payload.getRid());
            Schema schema = payload.getSchema();
            String tableName = schema.getName();
            if (StringUtils.isNotEmpty(tableName)) {
                if (!tableList.contains(tableName)) {
                    continue;
                }
                redoLogContent.setTableName(tableName);
                redoLogContent.setSegOwner(schema.getOwner());
            }
            redoLogContent.setRowId(payload.getRid());
            Map<String, Object> beforeMap = new HashMap<>();
            payload.getBeforeList().forEach(b -> beforeMap.put(b.getColumnName(), getColumnValue(b)));
            redoLogContent.setUndoRecord(beforeMap);

            Map<String, Object> afterMap = new HashMap<>();
            payload.getAfterList().forEach(a -> afterMap.put(a.getColumnName(), getColumnValue(a)));
            redoLogContent.setRedoRecord(afterMap);

            if (op == RedologOp.DELETE && EmptyKit.isEmpty(afterMap)) {
                redoLogContent.setRedoRecord(beforeMap);
            }

            if (op == RedologOp.DDL) {
                redoLogContent.setSqlRedo(payload.getDdl());
            }
            resultList.add(redoLogContent);
        }
        return resultList;
    }

    private Object getColumnValue(Value value) {
        Value.DatumCase datumCase = value.getDatumCase();
        switch (datumCase.getNumber()) {
            case 2:
                return value.getValueInt();
            case 3:
                return value.getValueFloat();
            case 4:
                return value.getValueDouble();
            case 5:
                return value.getValueString();
            case 6:
                return value.getValueBytes();
        }
        return null;
    }
}

package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.kit.EmptyKit;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicBoolean;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

public class AutoRedoOracleLogMiner extends OracleLogMiner {

    public AutoRedoOracleLogMiner(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws SQLException {
        super(oracleJdbcContext, connectorId, tapLogger);
    }

    @Override
    public void startMiner() throws Throwable {
        setSession();
        //1、check whether archived log exists
        AtomicBoolean notExistsArchivedLog = new AtomicBoolean(false);
        tapLogger.info("Checking whether archived log exists...");
        oracleJdbcContext.queryWithNext(CHECK_ARCHIVED_LOG_EXISTS, resultSet -> notExistsArchivedLog.set(resultSet.getInt(1) <= 0));
        //2、find lastScn from offset
        Long lastScn = oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn();
        //3、if there is no archived log, get first online log
        if (notExistsArchivedLog.get()) {
            RedoLog redoLog = firstOnlineRedoLog(lastScn);
            if (EmptyKit.isNull(redoLog)) {
                throw new Exception(String.format("SCN %s is not included in the online log", lastScn));
            }
            //4、add log file for logMiner
            statement.execute(String.format(ADD_REDO_LOG_FILE_FOR_LOGMINER, redoLog.getName()));
            lastScn = lastScn < redoLog.getFirstChangeScn() ? redoLog.getFirstChangeScn() : lastScn;
        }
        //5、if necessary store in dictionary
        oracleJdbcContext.queryWithNext(String.format(LAST_DICT_ARCHIVE_LOG_BY_SCN, lastScn), resultSet -> {
            if (resultSet.getRow() <= 0) {
                tapLogger.info("building new log file...");
                oracleJdbcContext.execute(STORE_DICT_IN_REDO_SQL);
            }
        });
        //7、start logMiner
        tapLogger.info("Redo Log Miner is starting...");
        statement.execute(String.format(START_LOG_MINOR_CONTINUOUS_MINER_SQL, lastScn));
        isRunning.set(true);
        initRedoLogQueueAndThread();
        tapLogger.info("Redo Log Miner has been started...");
        consumer.streamReadStarted();
        //8、begin to analyze logs
        try {
            statement.setFetchSize(oracleConfig.getFetchSize());
            resultSet = statement.executeQuery(analyzeLogSql(lastScn - 1));
            while (isRunning.get() && EmptyKit.isNotNull(resultSet) && resultSet.next()) {
                if (EmptyKit.isNotNull(threadException.get())) {
                    throw new RuntimeException(threadException.get());
                }
                while (ddlStop.get()) {
                    TapSimplify.sleep(1000);
                }
                analyzeLog(resultSet);
            }
        } catch (Exception e) {
            if (!isRunning.get()) {
                tapLogger.info("Auto redo oracle log miner result set closed");
            } else {
                throw e;
            }
        }
    }

}

package io.tapdata.connector.oracle.cdc.bridge;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.tapdata.common.concurrent.ConcurrentProcessor;
import io.tapdata.common.concurrent.TapExecutors;
import io.tapdata.common.ddl.DDLFactory;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner;
import io.tapdata.connector.oracle.cdc.logminer.handler.RawTypeHandler;
import io.tapdata.connector.oracle.cdc.logminer.handler.UnicodeStringColumnHandler;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.constant.TapLog;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.event.ddl.TapDDLEvent;
import io.tapdata.entity.event.ddl.TapDDLUnknownEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.exception.TapPdkRetryableEx;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.util.DateUtil;

import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static io.tapdata.base.ConnectorBase.list;

public class BridgeLogMiner3 extends OracleLogMiner {

    private String owner_table_scn;
    private long referenceTime = 0L;
    private String hexScn;
    private String pendingHexScn;
    private Long pendingTimestamp;
    private String lastScn;
    private Long lastScnSerialNo = 0L;
    private final AtomicBoolean isGBK = new AtomicBoolean(false);
    private String nls_lang;
    private final Map<String, String> dataFormatMap = new HashMap<>();
    private boolean invalid = false;
    private String dropTransactionId = null;
    private String xid = null;

    public BridgeLogMiner3(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws SQLException {
        super(oracleJdbcContext, connectorId, tapLogger);
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        hexScn = Long.toHexString(Long.parseLong(oracleOffset.getHexScn(), 16) - 1);
        lastScn = hexScn;
        owner_table_scn = tableList.stream().map(v -> oracleConfig.getSchema() + "." + v).collect(Collectors.joining("|")) + "|@" + hexScn;
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        hexScn = Long.toHexString(Long.parseLong(oracleOffset.getHexScn(), 16) - 1);
        owner_table_scn = schemaTableMap.entrySet().stream()
                .map(entry -> entry.getValue().stream()
                        .map(table -> entry.getKey() + "." + table)
                        .collect(Collectors.joining("|")))
                .collect(Collectors.joining("|")) + "|@" + hexScn;
    }

    @Override
    protected void makeLobTables() {

    }

    @Override
    protected void multiMakeLobTables() {

    }

    protected void makeOffset(Object offsetState) throws SQLException {
        if (EmptyKit.isNull(offsetState)) {
            oracleOffset = new OracleOffset();
            oracleOffset.setHexScn(Long.toHexString(findCurrentScn()));
        } else {
            oracleOffset = (OracleOffset) offsetState;
            if (EmptyKit.isNotNull(oracleOffset.getPendingHexScn()) && !"0".equals(oracleOffset.getPendingHexScn())) {
                if (oracleOffset.getTimestamp() - oracleOffset.getPendingTimestamp() > oracleConfig.getTransactionAliveMinutes() * 60 * 1000L) {
                    oracleOffset.setHexScn(Long.toHexString(findScnFromTimestamp(oracleOffset.getTimestamp() - oracleConfig.getTransactionAliveMinutes() * 60 * 1000L)));
                } else {
                    oracleOffset.setHexScn(oracleOffset.getPendingHexScn());
                }
            }
            if (EmptyKit.isNull(oracleOffset.getHexScn())) {
                if (EmptyKit.isNotNull(oracleOffset.getTimestamp())) {
                    if (System.currentTimeMillis() < oracleOffset.getTimestamp()) {
                        throw new RuntimeException("Timestamp can not be later than now!");
                    }
                    oracleOffset.setHexScn(Long.toHexString(findScnFromTimestamp(oracleOffset.getTimestamp())));
                } else if (EmptyKit.isNotNull(oracleOffset.getLastScn())) {
                    oracleOffset.setHexScn(Long.toHexString(oracleOffset.getLastScn()));
                } else {
                    oracleOffset.setHexScn(Long.toHexString(findCurrentScn()));
                }
            }
        }
    }

    private void isGBK() throws SQLException {
        oracleJdbcContext.query("select userenv('language') from dual where userenv('language') like '%GBK'", rs -> {
            if (rs.next()) {
                isGBK.set(true);
            }
        });
        nls_lang = isGBK.get() ? "GBK" : "UTF-8";
    }

    @Override
    public void startMiner() throws Throwable {
        isGBK();
        isRunning.set(true);
        try (
                BridgeService bridgeService = new BridgeService(oracleConfig.getRawLogServerHost(), oracleConfig.getRawLogServerPort(), connectorId);
                ConcurrentProcessor<OffsetEvent, OffsetEvent> concurrentProcessor = TapExecutors.createSimple(8, 32, "BridgeLogMiner-Processor")
        ) {
            bridgeService.setTapLogger(tapLogger);
            bridgeService.initTask();
            bridgeService.setEnableFzsZip(oracleConfig.getEnableFzsZip());
            bridgeService.setFzsSocketTimeout(oracleConfig.getFzsSocketTimeout());
            bridgeService.setFzsPollingInterval(oracleConfig.getPollingInterval() / 2);
            bridgeService.setManyTablesWithScn(owner_table_scn);
            bridgeService.start();

            Thread t = new Thread(() -> {
                byte[] temp = null;
                while (isRunning.get()) {
                    try {
                        temp = bridgeService.pullRedoLog();
                        if (temp == null) {
                            TapSimplify.sleep(oracleConfig.getPollingInterval() / 2);
                            continue;
                        }
                        handleBridgeEventWithProto(concurrentProcessor, temp);
                    } catch (Exception e) {
                        try (FileOutputStream fileOutputStream = new FileOutputStream("bridge_error.log", true)) {
                            fileOutputStream.write(temp);
                        } catch (Exception ignored) {
                        }
                        try {
                            tapLogger.warn("handle bridge event failed, [{}]", new String(temp, isGBK.get() ? "GBK" : "UTF-8"));
                        } catch (UnsupportedEncodingException ex) {
                            threadException.set(e);
                        }
                        threadException.set(e);
                    }
                }
            });
            t.setName("BridgeLogMiner-Producer");
            t.start();
            while (isRunning.get()) {
                List<TapEvent> events = list();
                OracleOffset lastOffset = null;
                int noHeartCount = 0;
                while (isRunning.get()) {
                    if (EmptyKit.isNotNull(threadException.get())) {
                        throw threadException.get();
                    }
                    OffsetEvent offsetEvent = concurrentProcessor.get(2, TimeUnit.SECONDS);
                    if (EmptyKit.isNotNull(offsetEvent)) {
                        noHeartCount = 0;
                        lastOffset = (OracleOffset) offsetEvent.getOffset();
                        events.add(offsetEvent.getEvent());
                        if (offsetEvent.getEvent() instanceof TapDDLEvent) {
                            consumer.accept(events, lastOffset);
                            events = list();
                            ddlFlush();
                            ddlStop.set(false);
                        } else if (events.size() >= recordSize) {
                            consumer.accept(events, lastOffset);
                            events = list();
                        }
                    } else {
                        if (events.size() > 0) {
                            consumer.accept(events, lastOffset);
                            events = list();
                        } else {
                            noHeartCount++;
                            if (noHeartCount > 100) {
                                throw new TapPdkRetryableEx("oracle", new RuntimeException("Heartbeat timeout, please check the network or the raw server"));
                            }
                        }
                    }
                }
            }
        }
        if (isRunning.get()) {
            throw new RuntimeException("Exception occurs in Bridge Log Miner service");
        }
    }

    public void handleBridgeEventWithProto(ConcurrentProcessor<OffsetEvent, OffsetEvent> concurrentProcessor, byte[] data) throws Exception {

        int idx = 0;
        int oplen;
        int opcode;
        int ownerLength;
        int tableLength;
        int columnNameLength;
        int columnNum;
        int columnLength;

        int len = data.length - 11;
        byte[] buf = new byte[len];
        System.arraycopy(data, 8, buf, 0, len);

        while (idx < len) {
            oplen = Byte.toUnsignedInt(buf[idx]) << 24 |
                    Byte.toUnsignedInt(buf[idx + 1]) << 16 |
                    Byte.toUnsignedInt(buf[idx + 2]) << 8 |
                    Byte.toUnsignedInt(buf[idx + 3]);
            opcode = Byte.toUnsignedInt(buf[idx + 4]);

            if (opcode == 82) {
                int i = idx + 39;
                long scnTime = (long) Byte.toUnsignedInt(buf[i++]) << 24 |
                        Byte.toUnsignedInt(buf[i++]) << 16 |
                        Byte.toUnsignedInt(buf[i++]) << 8 |
                        Byte.toUnsignedInt(buf[i]);
                long year = scnTime / 32140800 + 1988;
                long month = (scnTime / 2678400) % 12 + 1;
                long day = (scnTime / 86400) % 31 + 1;
                long hour = (scnTime / 3600) % 24;
                long minute = (scnTime / 60) % 60;
                long second = scnTime % 60;
                referenceTime = Timestamp.valueOf(String.format("%04d-%02d-%02d %02d:%02d:%02d", year, month, day, hour, minute, second)).getTime();
                String pHexScn = getHexScn(buf, idx + 8);
                if (!pHexScn.equals(pendingHexScn)) {
                    pendingHexScn = pHexScn;
                    pendingTimestamp = referenceTime;
                }
                hexScn = getHexScn(buf, idx + 16);
                concurrentProcessor.runAsync(new OffsetEvent(new HeartbeatEvent().init().referenceTime(referenceTime), getOffset()), this::parse);
            } else if (opcode == 209) { //209: ddl
                hexScn = getHexScn(buf, idx);
                xid = null;
                int i = idx + 27;
                ownerLength = Byte.toUnsignedInt(buf[i++]);
                i += ownerLength;
                tableLength = Byte.toUnsignedInt(buf[i++]);
                i += tableLength;

                int ddlSqlLength = Byte.toUnsignedInt(buf[i++]) << 24 |
                        Byte.toUnsignedInt(buf[i++]) << 16 |
                        Byte.toUnsignedInt(buf[i++]) << 8 |
                        Byte.toUnsignedInt(buf[i++]);
                String ddlSql = new String(buf, i, ddlSqlLength - 1, nls_lang);
                try {
                    DDLFactory.ddlToTapDDLEvent(ddlParserType, ddlSql,
                            DDL_WRAPPER_CONFIG,
                            tableMap,
                            tapDDLEvent -> {
                                tapDDLEvent.setTime(System.currentTimeMillis());
                                tapDDLEvent.setReferenceTime(referenceTime);
                                tapDDLEvent.setOriginDDL(ddlSql);
                                tapDDLEvent.setExactlyOnceId(hexScn);
                                concurrentProcessor.runAsync(new OffsetEvent(tapDDLEvent, getOffset()), this::parse);
                            });
                } catch (Throwable e) {
                    TapDDLEvent tapDDLEvent = new TapDDLUnknownEvent();
                    tapDDLEvent.setTime(System.currentTimeMillis());
                    tapDDLEvent.setReferenceTime(System.currentTimeMillis());
                    tapDDLEvent.setOriginDDL(ddlSql);
                    tapDDLEvent.setExactlyOnceId(hexScn);
                    concurrentProcessor.runAsync(new OffsetEvent(tapDDLEvent, getOffset()), this::parse);
                }
                ddlStop.set(true);
                while (ddlStop.get() && isRunning.get()) {
                    TapSimplify.sleep(500);
                }
            } else if (opcode == 187) { //187: qmi
                hexScn = getHexScn(buf, idx);
                xid = getHexScn(buf, idx + 10);
                AtomicInteger i = new AtomicInteger(idx + 52);
                ownerLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                String owner = new String(buf, i.get(), ownerLength - 1, nls_lang);
                i.addAndGet(ownerLength);
                tableLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                String tableName = new String(buf, i.get(), tableLength - 1, nls_lang);
                i.addAndGet(tableLength);

                int partLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                if (partLength > 0) {
                    i.addAndGet(partLength);
                }

                i.addAndGet(4); //skip is_index, is_full, if_can_dp, queue_id

                int rowCount = Byte.toUnsignedInt(buf[i.getAndIncrement()]) << 8 | Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                String[] columnNames = null;
                for (int ii = 0; ii < rowCount; ii++) {
                    if (checkEndByte(buf[i.get()])) {
                        i.addAndGet(3);
                    } else {
                        i.incrementAndGet();
                    }
                    i.addAndGet(2);
                    columnNum = getLength(buf, i, 2);
                    if (ii == 0) {
                        columnNames = new String[columnNum];
                    }
                    Map<String, Object> sdata = new HashMap<>();

                    for (int iii = 0; iii < columnNum; iii++) {
                        columnLength = getLength(buf, i, 4);
                        byte[] columnDataBytes = new byte[columnLength];
                        System.arraycopy(buf, i.get(), columnDataBytes, 0, columnLength);
                        i.addAndGet(columnLength);
                        if (ii == 0) {
                            columnNameLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                            columnNames[iii] = new String(buf, i.get(), columnNameLength - 1, nls_lang);
                            i.addAndGet(columnNameLength);

                            getLength(buf, i, 2);
                            getLength(buf, i, 4);
                            i.addAndGet(5);
                        }
                        sdata.put(columnNames[iii], columnDataBytes);
                    }
//                    for (Map.Entry<String, Object> entry : sdata.entrySet()) {
//                        parseKeyValueWithProto(getSchemaAndTable(owner, tableName), entry, nls_lang);
//                    }
                    TapRecordEvent recordEvent = new TapInsertRecordEvent().init()
                            .table(tableName)
                            .after(sdata);
                    recordEvent.schema(owner);
                    recordEvent.setReferenceTime(referenceTime);
                    if (Objects.equals(hexScn, lastScn)) {
                        recordEvent.setExactlyOnceId(hexScn + "_" + lastScnSerialNo++);
                    } else {
                        lastScn = hexScn;
                        lastScnSerialNo = 0L;
                        recordEvent.setExactlyOnceId(hexScn + "_" + lastScnSerialNo++);
                    }
                    if (withSchema) {
                        recordEvent.setNamespaces(Lists.newArrayList(owner, tableName));
                    }
                    if (!isTapTransaction(tableName)) {
                        concurrentProcessor.runAsync(new OffsetEvent(recordEvent, getOffset()), this::parse);
                    }
                }
            } else {
                TapRecordEvent recordEvent;
                String owner;
                String tableName;
                if (opcode == 178 || opcode == 179) {  //idrp &drp
                    hexScn = getHexScn(buf, idx);
                    xid = getHexScn(buf, idx + 10);
                    AtomicInteger i = new AtomicInteger(idx + 64);
                    ownerLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                    owner = new String(buf, i.get(), ownerLength - 1, nls_lang);
                    i.addAndGet(ownerLength);

                    tableLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                    tableName = new String(buf, i.get(), tableLength - 1, nls_lang);
                    i.addAndGet(tableLength);

                    i.incrementAndGet();  //skip is_index

                    columnNum = getLength(buf, i, 2);

                    Map<String, Object> sdata = new HashMap<>();
                    for (int ii = 0; ii < columnNum; ii++) {
                        columnLength = getLength(buf, i, 4);
                        byte[] columnDataBytes = new byte[columnLength];
                        System.arraycopy(buf, i.get(), columnDataBytes, 0, columnLength);
                        i.addAndGet(columnLength);

                        columnNameLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                        String columnName = new String(buf, i.get(), columnNameLength - 1, nls_lang);
                        i.addAndGet(columnNameLength);

                        getLength(buf, i, 2);
                        getLength(buf, i, 4);

                        i.addAndGet(5);
                        sdata.put(columnName, columnDataBytes);
                    }
//                    for (Map.Entry<String, Object> entry : sdata.entrySet()) {
//                        parseKeyValueWithProto(getSchemaAndTable(owner, tableName), entry, nls_lang);
//                    }
                    if (opcode == 178) {
                        recordEvent = new TapInsertRecordEvent().init().table(tableName).after(sdata);
                    } else {
                        recordEvent = new TapDeleteRecordEvent().init().table(tableName).before(sdata);
                    }
                } else if (opcode == 181) {  //181:urp
                    hexScn = getHexScn(buf, idx);
                    xid = getHexScn(buf, idx + 10);
                    AtomicInteger i = new AtomicInteger(idx + 65);

                    ownerLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                    owner = new String(buf, i.get(), ownerLength - 1, nls_lang);
                    i.addAndGet(ownerLength);

                    tableLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                    tableName = new String(buf, i.get(), tableLength - 1, nls_lang);
                    i.addAndGet(tableLength);

                    i.incrementAndGet();  //skip is_index

                    columnNum = getLength(buf, i, 2);

                    int afterColumnNum = getLength(buf, i, 2);

                    int afterColumnNameLength;
                    int afterColumnLength;
                    Map<String, Object> after = new HashMap<>();
                    for (int ii = 0; ii < afterColumnNum; ii++) {
                        getLength(buf, i, 2);

                        afterColumnLength = getLength(buf, i, 4);
                        byte[] afterColumnDataBytes = new byte[afterColumnLength];
                        System.arraycopy(buf, i.get(), afterColumnDataBytes, 0, afterColumnLength);
                        i.addAndGet(afterColumnLength);


                        afterColumnNameLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                        String afterColumnName = new String(buf, i.get(), afterColumnNameLength - 1, nls_lang);
                        i.addAndGet(afterColumnNameLength);

                        getLength(buf, i, 2);
                        getLength(buf, i, 4);

                        i.addAndGet(4);

                        after.put(afterColumnName, afterColumnDataBytes);
                    }

                    Map<String, Object> before = new HashMap<>();
                    int beforeColumnNum;
                    int beforeColumnLength;
                    int beforeColumnNameLength;
                    if (columnNum != 0) {
                        beforeColumnNum = getLength(buf, i, 2);

                        for (int iii = 0; iii < beforeColumnNum; iii++) {
                            getLength(buf, i, 2);

                            beforeColumnLength = getLength(buf, i, 4);
                            byte[] beforeColumnDataBytes = new byte[beforeColumnLength];
                            System.arraycopy(buf, i.get(), beforeColumnDataBytes, 0, beforeColumnLength);
                            i.addAndGet(beforeColumnLength);

                            beforeColumnNameLength = Byte.toUnsignedInt(buf[i.getAndIncrement()]);
                            String beforeColumnName = new String(buf, i.get(), beforeColumnNameLength - 1, nls_lang);
                            i.addAndGet(beforeColumnNameLength);

                            getLength(buf, i, 2);
                            getLength(buf, i, 4);

                            i.addAndGet(3);
                            before.put(beforeColumnName, beforeColumnDataBytes);
                        }
                    }

//                    for (Map.Entry<String, Object> entry : after.entrySet()) {
//                        parseKeyValueWithProto(getSchemaAndTable(owner, tableName), entry, nls_lang);
//                    }
//                    for (Map.Entry<String, Object> entry : before.entrySet()) {
//                        parseKeyValueWithProto(getSchemaAndTable(owner, tableName), entry, nls_lang);
//                        if (!after.containsKey(entry.getKey())) {
//                            after.put(entry.getKey(), entry.getValue());
//                        }
//                    }
                    recordEvent = new TapUpdateRecordEvent().init().table(tableName).after(after).before(before);
                    i.getAndIncrement();

                } else {
                    idx = idx + oplen + 4;
                    continue;
                }
                recordEvent.schema(owner);
                recordEvent.setReferenceTime(referenceTime);
                if (Objects.equals(hexScn, lastScn)) {
                    recordEvent.setExactlyOnceId(hexScn + "_" + lastScnSerialNo++);
                } else {
                    lastScn = hexScn;
                    lastScnSerialNo = 0L;
                    recordEvent.setExactlyOnceId(hexScn + "_" + lastScnSerialNo++);
                }
                if (withSchema) {
                    recordEvent.setNamespaces(Lists.newArrayList(owner, tableName));
                }
                if (!isTapTransaction(tableName)) {
                    concurrentProcessor.runAsync(new OffsetEvent(recordEvent, getOffset()), this::parse);
                }
            }
            idx = idx + oplen + 4;
        }
    }

    private OffsetEvent parse(OffsetEvent v) {
        TapEvent event = v.getEvent();
        if (event instanceof TapInsertRecordEvent) {
            TapInsertRecordEvent insertRecordEvent = (TapInsertRecordEvent) event;
            for (Map.Entry<String, Object> entry : insertRecordEvent.getAfter().entrySet()) {
                parseKeyValueWithProto(getSchemaAndTable(insertRecordEvent.getSchema(), insertRecordEvent.getTableId()), entry, nls_lang);
            }
        } else if (event instanceof TapDeleteRecordEvent) {
            TapDeleteRecordEvent deleteRecordEvent = (TapDeleteRecordEvent) event;
            for (Map.Entry<String, Object> entry : deleteRecordEvent.getBefore().entrySet()) {
                parseKeyValueWithProto(getSchemaAndTable(deleteRecordEvent.getSchema(), deleteRecordEvent.getTableId()), entry, nls_lang);
            }
        } else if (event instanceof TapUpdateRecordEvent) {
            TapUpdateRecordEvent updateRecordEvent = (TapUpdateRecordEvent) event;
            for (Map.Entry<String, Object> entry : updateRecordEvent.getAfter().entrySet()) {
                parseKeyValueWithProto(getSchemaAndTable(updateRecordEvent.getSchema(), updateRecordEvent.getTableId()), entry, nls_lang);
            }
            for (Map.Entry<String, Object> entry : updateRecordEvent.getBefore().entrySet()) {
                parseKeyValueWithProto(getSchemaAndTable(updateRecordEvent.getSchema(), updateRecordEvent.getTableId()), entry, nls_lang);
                if (!updateRecordEvent.getAfter().containsKey(entry.getKey())) {
                    updateRecordEvent.getAfter().put(entry.getKey(), entry.getValue());
                }
            }
        }
        return v;
    }

    private OracleOffset getOffset() {
        OracleOffset offset = new OracleOffset();
        offset.setHexScn(hexScn);
        offset.setPendingHexScn(pendingHexScn);
        offset.setPendingTimestamp(pendingTimestamp);
        offset.setTimestamp(referenceTime);
        return offset;
    }

    private boolean isTapTransaction(String tableName) {
        //双活情形下，需要过滤_tap_double_active记录的同事务数据
        if (Boolean.TRUE.equals(oracleConfig.getDoubleActive())) {
            if ("_tap_double_active".equals(tableName)) {
                dropTransactionId = xid;
                return true;
            } else {
                if (null != dropTransactionId) {
                    if (dropTransactionId.equals(xid)) {
                        return true;
                    } else {
                        dropTransactionId = null;
                    }
                }
            }
        }
        return false;
    }

    private void handleBridgeEvent(String data) {
        tapLogger.debug(data);
        AtomicReference<List<TapEvent>> eventList = new AtomicReference<>(new ArrayList<>());
        String hexScn = null;
        JSONArray logArray;
        try {
            logArray = JSONObject.parseObject(data).getJSONArray("op");
        } catch (Exception e) {
            tapLogger.warn("parse log data failed: {}", data);
            return;
        }
        for (int i = 0; i < logArray.size(); i++) {
            JSONObject logObject = logArray.getJSONObject(i);
            String schema = logObject.getString("owner");
            String tableName = logObject.getString("table");
            String schemaAndTable = tableName;
            if (withSchema) {
                schemaAndTable = schema + "." + tableName;
            }
            String opType = logObject.getString("type");
            try {
                referenceTime = LocalDateTime.parse(logObject.getString("scntime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(oracleConfig.getSysZoneId()).toEpochSecond() * 1000;
            } catch (Exception ignored) {
            }
            hexScn = logObject.getString("scn");
            if ("DDL".equals(opType)) {
                JSONArray ddlList = logObject.getJSONArray("sql");
                for (int j = 0; j < ddlList.size(); j++) {
                    ddlList.getJSONObject(j).forEach((key, value) -> {
                        try {
                            DDLFactory.ddlToTapDDLEvent(ddlParserType, String.valueOf(value),
                                    DDL_WRAPPER_CONFIG,
                                    tableMap,
                                    tapDDLEvent -> {
                                        tapDDLEvent.setTime(System.currentTimeMillis());
                                        tapDDLEvent.setReferenceTime(referenceTime);
                                        eventList.get().add(tapDDLEvent);
                                    });
                        } catch (Throwable e) {
                            tapLogger.warn("DDL parse failed, [{}]", value);
                        }
                    });
                }
            } else if ("QMI".equals(opType)) {
                for (Object row : logObject.getJSONArray("rows")) {
                    Map<String, Object> after = new HashMap<>();
                    ((JSONArray) row).stream().map(v -> (JSONObject) v).forEach(col -> after.putAll(col.getInnerMap()));
                    for (Map.Entry<String, Object> entry : after.entrySet()) {
                        parseKeyValue(schemaAndTable, entry);
                    }
                    if (after.entrySet().stream().allMatch(entry -> EmptyKit.isNull(entry.getValue()))) {
                        tapLogger.warn("IRP all value is null, [{}]", data);
                        continue;
                    }
                    TapRecordEvent recordEvent = new TapInsertRecordEvent().init()
                            .table(tableName)
                            .after(after);
                    recordEvent.setReferenceTime(referenceTime);
                    if (withSchema) {
                        recordEvent.setNamespaces(Lists.newArrayList(schema, tableName));
                    }
                    eventList.get().add(recordEvent);
                }
            } else {
                TapRecordEvent recordEvent;
                switch (opType) {
                    case "IRP": {
                        Map<String, Object> after = new HashMap<>();
                        logObject.getJSONArray("col").stream().map(v -> (JSONObject) v).forEach(col -> after.putAll(col.getInnerMap()));
                        for (Map.Entry<String, Object> entry : after.entrySet()) {
                            parseKeyValue(schemaAndTable, entry);
                        }
                        if (after.entrySet().stream().allMatch(entry -> EmptyKit.isNull(entry.getValue()))) {
                            tapLogger.warn("IRP all value is null, [{}]", data);
                            continue;
                        }
                        recordEvent = new TapInsertRecordEvent().init()
                                .table(tableName)
                                .after(after);
                        break;
                    }
                    case "URP": {
                        Map<String, Object> before = new HashMap<>();
                        Map<String, Object> after = new HashMap<>();
                        logObject.getJSONArray("col_where").stream().map(v -> (JSONObject) v).forEach(col -> {
                            before.putAll(col.getInnerMap());
                            after.putAll(col.getInnerMap());
                        });
                        logObject.getJSONArray("col_set").stream().map(v -> (JSONObject) v).forEach(col -> after.putAll(col.getInnerMap()));
                        for (Map.Entry<String, Object> entry : before.entrySet()) {
                            parseKeyValue(schemaAndTable, entry);
                        }
                        for (Map.Entry<String, Object> entry : after.entrySet()) {
                            parseKeyValue(schemaAndTable, entry);
                        }
                        if (after.entrySet().stream().allMatch(entry -> EmptyKit.isNull(entry.getValue()))) {
                            tapLogger.warn("URP all value is null, [{}]", data);
                            continue;
                        }
                        recordEvent = new TapUpdateRecordEvent().init()
                                .table(tableName)
                                .after(after)
                                .before(before);
                        break;
                    }
                    case "DRP": {
                        Map<String, Object> before = new HashMap<>();
                        logObject.getJSONArray("col").stream().map(v -> (JSONObject) v).forEach(col -> before.putAll(col.getInnerMap()));
                        for (Map.Entry<String, Object> entry : before.entrySet()) {
                            parseKeyValue(schemaAndTable, entry);
                        }
                        recordEvent = new TapDeleteRecordEvent().init()
                                .table(tableName)
                                .before(before);
                        break;
                    }
                    default:
                        continue;
                }
                recordEvent.setReferenceTime(referenceTime);
                if (withSchema) {
                    recordEvent.setNamespaces(Lists.newArrayList(schema, tableName));
                }
                eventList.get().add(recordEvent);
            }
            if (eventList.get().size() >= recordSize) {
                OracleOffset offset = new OracleOffset();
                offset.setHexScn(hexScn);
                offset.setPendingHexScn(pendingHexScn);
                offset.setPendingTimestamp(pendingTimestamp);
                offset.setTimestamp(referenceTime);
                consumer.accept(eventList.get(), offset);
                eventList.set(new ArrayList<>());
            }
        }
        if (EmptyKit.isNotEmpty(eventList.get())) {
            OracleOffset offset = new OracleOffset();
            offset.setHexScn(hexScn);
            offset.setPendingHexScn(pendingHexScn);
            offset.setPendingTimestamp(pendingTimestamp);
            offset.setTimestamp(referenceTime);
            consumer.accept(eventList.get(), offset);
        }
    }

    protected void parseKeyValue(String table, Map.Entry<String, Object> stringObjectEntry) {
        Object value = stringObjectEntry.getValue();
        String column = stringObjectEntry.getKey();
        Integer columnType = columnTypeMap.get(table + "." + column);
        if (EmptyKit.isNull(value) || !(value instanceof String) || EmptyKit.isNull(columnType)) {
            return;
        }
        if (EmptyKit.isEmpty(String.valueOf(value))) {
            stringObjectEntry.setValue(null);
            return;
        }
        try {
            switch (columnType) {
                case Types.BIGINT:
                    stringObjectEntry.setValue(new BigDecimal((String) value).longValue());
                    break;
                case Types.BINARY:
                case Types.LONGVARBINARY:
                case Types.VARBINARY:
                    stringObjectEntry.setValue(RawTypeHandler.parseRaw((String) value));
                    break;
                case Types.BIT:
                case Types.BOOLEAN:
                    stringObjectEntry.setValue(Boolean.valueOf((String) value));
                    break;
                case Types.CHAR:
                case Types.LONGNVARCHAR:
                case Types.LONGVARCHAR:
                case Types.VARCHAR:
                case Types.ROWID:
                case Types.ARRAY:
                case Types.DATALINK:
                case Types.DISTINCT:
                case Types.JAVA_OBJECT:
                case Types.NULL:
                case Types.OTHER:
                case Types.REF:
                case Types.REF_CURSOR:
                case Types.SQLXML:
                case Types.STRUCT:
                case Types.TIME_WITH_TIMEZONE:
                    break;
                case Types.NCHAR:
                case Types.NVARCHAR:
                    stringObjectEntry.setValue(UnicodeStringColumnHandler.getUnicdeoString((String) value));
                    break;
                case Types.DECIMAL:
                case Types.NUMERIC:
                case Types.DOUBLE:
                    stringObjectEntry.setValue(new BigDecimal((String) value));
                    break;
                case Types.FLOAT:
                case Types.REAL:
                    stringObjectEntry.setValue(new BigDecimal((String) value).floatValue());
                    break;
                case Types.INTEGER:
                    stringObjectEntry.setValue(new BigDecimal((String) value).intValue());
                    break;
                case Types.SMALLINT:
                case Types.TINYINT:
                    stringObjectEntry.setValue(new BigDecimal((String) value).shortValue());
                    break;
                case Types.DATE:
                case Types.TIME:
                case Types.TIMESTAMP:
                case Types.TIMESTAMP_WITH_TIMEZONE:
                case TIMESTAMP_TZ_TYPE:
                case TIMESTAMP_LTZ_TYPE:
                    String dataFormat = dataFormatMap.get(table + "." + column);
                    if (EmptyKit.isNull(dataFormat)) {
                        dataFormat = DateUtil.determineDateFormat((String) value);
                        dataFormatMap.put(table + "." + column, dataFormat);
                    }
                    stringObjectEntry.setValue(DateUtil.parseInstant((String) value, dataFormat));
                    break;
                case Types.BLOB:
                    stringObjectEntry.setValue(StringKit.toByteArray((String) value));
                    break;
                case Types.CLOB:
                case Types.NCLOB:
                    if ("EMPTY_BLOB()".equals(value)) {
                        stringObjectEntry.setValue(null);
                    }
                    break;
            }
        } catch (Exception e) {
            tapLogger.warn(TapLog.W_CONN_LOG_0014.getMsg(), value, columnType, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public void parseKeyValueWithProto(String table, Map.Entry<String, Object> stringObjectEntry, String nls_lang) {
        Object value = stringObjectEntry.getValue();
        String column = stringObjectEntry.getKey();
        Integer columnType = columnTypeMap.get(table + "." + column);
        if (EmptyKit.isNull(value) || !(value instanceof byte[]) || EmptyKit.isNull(columnType)) {
            return;
        }
        if (((byte[]) value).length == 0) {
            stringObjectEntry.setValue(null);
            return;
        }
        try {
            switch (columnType) {
                case Types.BIGINT:
                    stringObjectEntry.setValue(new BigDecimal(new String((byte[]) value, nls_lang)).longValue());
                    break;
                case Types.BINARY:
                case Types.LONGVARBINARY:
                case Types.VARBINARY:
                    stringObjectEntry.setValue(RawTypeHandler.parseRaw(new String((byte[]) value, nls_lang)));
                    break;
                case Types.BIT:
                case Types.BOOLEAN:
                    stringObjectEntry.setValue(Boolean.valueOf(new String((byte[]) value, nls_lang)));
                    break;
                case Types.CHAR:
                case Types.LONGNVARCHAR:
                case Types.LONGVARCHAR:
                case Types.VARCHAR:
                case Types.ROWID:
                case Types.ARRAY:
                case Types.DATALINK:
                case Types.DISTINCT:
                case Types.JAVA_OBJECT:
                case Types.NULL:
                case Types.OTHER:
                case Types.REF:
                case Types.REF_CURSOR:
                case Types.SQLXML:
                case Types.STRUCT:
                case Types.TIME_WITH_TIMEZONE:
                case Types.NCHAR:
                case Types.NVARCHAR:
                case INTERVAL_YM_TYPE:
                case INTERVAL_DS_TYPE:
                    stringObjectEntry.setValue(new String((byte[]) value, nls_lang));
                    break;
                case Types.DECIMAL:
                case Types.NUMERIC:
                case Types.DOUBLE:
                    String numberStr = new String((byte[]) value, nls_lang);
                    try {
                        stringObjectEntry.setValue(new BigDecimal(numberStr));
                    } catch (Exception e) {
                        stringObjectEntry.setValue(0);
                    }
//                    stringObjectEntry.setValue(new BigDecimal(new String((byte[]) value, nls_lang)));
                    break;
                case Types.FLOAT:
                case Types.REAL:
                    stringObjectEntry.setValue(new BigDecimal(new String((byte[]) value, nls_lang)).floatValue());
                    break;
                case Types.INTEGER:
                    stringObjectEntry.setValue(new BigDecimal(new String((byte[]) value, nls_lang)).intValue());
                    break;
                case Types.SMALLINT:
                case Types.TINYINT:
                    stringObjectEntry.setValue(new BigDecimal(new String((byte[]) value, nls_lang)).shortValue());
                    break;
                case Types.DATE:
                case Types.TIME:
                case Types.TIMESTAMP:
                case Types.TIMESTAMP_WITH_TIMEZONE:
                case TIMESTAMP_TZ_TYPE:
                case TIMESTAMP_LTZ_TYPE:
                    String str = new String((byte[]) value, nls_lang);
                    String dataFormat;
                    if (columnType == TIMESTAMP_TZ_TYPE && str.length() > 35) {
                        str = str.substring(0, 29);
                        if (invalid) {
                            dataFormat = dataFormatMap.get(table + "." + column);
                            if (EmptyKit.isNull(dataFormat)) {
                                dataFormat = DateUtil.determineDateFormat(str);
                                dataFormatMap.put(table + "." + column, dataFormat);
                            }
                        } else {
                            dataFormat = DateUtil.determineDateFormat(str);
                            dataFormatMap.put(table + "." + column, dataFormat);
                            invalid = true;
                        }
                    } else {
                        if (invalid) {
                            dataFormat = DateUtil.determineDateFormat(str);
                            dataFormatMap.put(table + "." + column, dataFormat);
                            invalid = false;
                        } else {
                            dataFormat = dataFormatMap.get(table + "." + column);
                            if (EmptyKit.isNull(dataFormat)) {
                                dataFormat = DateUtil.determineDateFormat(str);
                                dataFormatMap.put(table + "." + column, dataFormat);
                            }
                        }
                    }
                    if (columnType == TIMESTAMP_TZ_TYPE) {
                        stringObjectEntry.setValue(DateUtil.parseInstantWithZone(str, dataFormat, ZoneOffset.UTC));
                    } else {
                        stringObjectEntry.setValue(DateUtil.parseInstantWithHour(str, dataFormat, oracleConfig.getZoneOffsetHour()));
                    }
                    break;
                case Types.BLOB:
                    stringObjectEntry.setValue(StringKit.toByteArray(new String((byte[]) value, nls_lang)));
                    break;
                case Types.CLOB:
                case Types.NCLOB:
                    if ("EMPTY_BLOB()".equals(new String((byte[]) value, nls_lang))) {
                        stringObjectEntry.setValue(null);
                    } else {
                        stringObjectEntry.setValue(new String((byte[]) value, nls_lang));
                    }
                    break;
            }
        } catch (Exception e) {
            tapLogger.warn(TapLog.W_CONN_LOG_0014.getMsg(), table + "." + column, new String((byte[]) value), columnType, e);
            throw new RuntimeException(e);
        }
    }

    private String getSchemaAndTable(String schema, String tableName) {
        if (Boolean.TRUE.equals(withSchema)) {
            return schema + "." + tableName;
        }
        return tableName;
    }

    private boolean checkEndByte(byte b) {
        return (b & 0xFF) == 0xFF;
    }

    private String getHexScn(byte[] buf, int idx) {
        int i = idx + 9;
        long lscn = Byte.toUnsignedLong(buf[i++]) << 56 |
                Byte.toUnsignedLong(buf[i++]) << 48 |
                Byte.toUnsignedLong(buf[i++]) << 40 |
                Byte.toUnsignedLong(buf[i++]) << 32 |
                Byte.toUnsignedLong(buf[i++]) << 24 |
                Byte.toUnsignedLong(buf[i++]) << 16 |
                Byte.toUnsignedLong(buf[i++]) << 8 |
                Byte.toUnsignedLong(buf[i]);
        return Long.toHexString(lscn);
    }

    private int getLength(byte[] buf, AtomicInteger i, int bit) {
        if (checkEndByte(buf[i.get()])) {
            i.getAndIncrement();
            int res = 0;
            for (int j = bit - 1; j >= 0; j--) {
                res |= (Byte.toUnsignedInt(buf[i.getAndIncrement()]) << (8 * j));
            }
            return res;
        } else {
            return Byte.toUnsignedInt(buf[i.getAndIncrement()]);
        }
    }

    static class OffsetEvent {
        private final Object offset;
        private final TapEvent event;

        public OffsetEvent(TapEvent event, Object offset) {
            this.offset = offset;
            this.event = event;
        }

        public Object getOffset() {
            return offset;
        }

        public TapEvent getEvent() {
            return event;
        }
    }
}

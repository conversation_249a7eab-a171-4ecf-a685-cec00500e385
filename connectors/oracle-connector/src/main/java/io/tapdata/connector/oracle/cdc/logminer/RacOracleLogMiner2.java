package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.common.cdc.LogTransaction;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.util.JdbcUtil;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.exception.TapPdkOffsetOutOfLogEx;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.END_LOG_MINOR_SQL;
import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.START_LOG_MINOR_SQL;

public class RacOracleLogMiner2 extends OracleLogMiner {

    private long continuousScn; //稳定推进的SCN，兜底不丢数
    private long loopScn; //循环推进的SCN，会根据情况回退
    private final int threadSize;
    private int retryCount = 0;

    public RacOracleLogMiner2(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger, int threadSize) throws Throwable {
        super(oracleJdbcContext, connectorId, tapLogger);
        continuousScn = 0;
        loopScn = 0;
        this.threadSize = threadSize;
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    protected void readyForInit() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
    }

    private List<RedoLog> getArchiveAndOnlineLogWithScn(Long startScn) throws SQLException {
        List<RedoLog> redoLogs = new ArrayList<>();
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(SQL_QUERY_LOGFILE)
        ) {
            preparedStatement.setLong(1, startScn);
            preparedStatement.setLong(2, startScn);
            preparedStatement.setLong(3, startScn);
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                while (resultSet.next()) {
                    redoLogs.add(wrapRedoLog(resultSet));
                }
            }
        }
        if (redoLogs.size() != threadSize) {
            retryCount++;
            if (retryCount > 50) {
                throw new TapPdkOffsetOutOfLogEx("oracle", oracleOffset, new RuntimeException("redo log files size not match, check redo log files"));
            }
            tapLogger.warn("【rac miner】redo log files size not match, redoLogs: {}, retryCount: {}", redoLogs, retryCount);
            TapSimplify.sleep(2000);
            return getArchiveAndOnlineLogWithScn(startScn);
        }
        retryCount = 0;
        return redoLogs;
    }

    private RedoLog wrapRedoLog(ResultSet resultSet) throws SQLException {
        RedoLog redoLog = new RedoLog();
        redoLog.setName(resultSet.getString("NAME"));
        redoLog.setFirstChangeScn(resultSet.getLong("FIRST_CHANGE#"));
        //在线的next_change#超过了long的最大值，SQL将next_change#强制为空，代码中为了方便比较next_change#置为Long.MAX_VALUE
        if (EmptyKit.isNull(resultSet.getString("NEXT_CHANGE#"))) {
            redoLog.setNextChangeScn(Long.MAX_VALUE);
        } else {
            redoLog.setNextChangeScn(resultSet.getLong("NEXT_CHANGE#"));
        }
        redoLog.setSequence(resultSet.getLong("SEQUENCE#"));
        redoLog.setThread(resultSet.getLong("THREAD#"));
        redoLog.setOnlineRedo("online".equals(resultSet.getString("STATUS")));
        redoLog.setCreator(resultSet.getString("CREATOR"));
        return redoLog;
    }

    private static final String SQL_QUERY_LOGFILE = "SELECT 'archived' status, t.NAME, t.THREAD#, t.sequence#, t.first_change#, t.next_change#, t.creator\n" +
            "from v$archived_log t\n" +
            "where (NAME IS NOT NULL AND DEST_ID != 0 AND STATUS = 'A' AND STANDBY_DEST = 'NO' AND IS_RECOVERY_DEST_FILE = 'NO')\n" +
            "  and next_change# > ? AND  first_change# <= ?\n" +
            "union ALL\n" +
            "select 'online', max(lf.MEMBER) NAME, l.THREAD#, l.sequence#, l.first_change#, null, null\n" +
            "FROM v$log l\n" +
            "         LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP#\n" +
            "where l.status = 'CURRENT' AND lf.IS_RECOVERY_DEST_FILE = 'NO' AND first_change# <= ?\n" +
            "group by l.THREAD#, l.sequence#, l.first_change#";

    @Override
    public void startMiner() throws Throwable {
        setSession();
        //挖掘起始scn
        continuousScn = oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn();
        tapLogger.info("total start mining scn: " + continuousScn);
        List<RedoLog> redoLogsTemp = new ArrayList<>();
        while (isRunning.get()) {
            //全null值
            boolean allNull = false;
            //1、首先归档文件中查询该scn对应的文件(归档与在线日志同时查询)
            List<RedoLog> redoLogs = getArchiveAndOnlineLogWithScn(continuousScn);
//            tapLogger.info("【rac miner】redo log files, redoLogsTemp:{}, redoLogs: {}", redoLogsTemp, redoLogs);
            if (EmptyKit.isEmpty(redoLogs)) {
                throw new TapPdkOffsetOutOfLogEx("oracle", oracleOffset, new RuntimeException("redo log files has been cleared, please check"));
            }
            if ((redoLogs.size() != redoLogsTemp.size()) || redoLogsTemp.stream().anyMatch(log -> redoLogs.stream().noneMatch(log::equals))) {
                if (EmptyKit.isNotEmpty(redoLogsTemp)) {
                    EmptyKit.closeQuietly(resultSet);
                    ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
                }
                String addLogMinerSql = createAddLogMinerSql(redoLogs);
                if (EmptyKit.isBlank(addLogMinerSql)) {
                    throw new TapPdkOffsetOutOfLogEx("oracle", oracleOffset, new RuntimeException("redo log files has been cleared, please check"));
                }
                statement.execute(addLogMinerSql);
                tapLogger.info("【rac miner】add log miner sql: {}", addLogMinerSql);
                statement.execute(START_LOG_MINOR_SQL);
                statement.setFetchSize(1000);
                loopScn = continuousScn;
            } else {
                TapSimplify.sleep(oracleConfig.getPollingInterval());
                loopScn = instanceThreadMindedSCNMap.values().stream().min(Long::compareTo).orElse(continuousScn) + 1;
                //如果redoLogs没有变化，说明当前scn对应的日志文件没有变化，直接跳过
                continuousScn = loopScn;
            }
            //2、查询scnMap中最小scn的thread
//            tapLogger.info("【rac miner】instanceThreadMindedSCNMap: {}", instanceThreadMindedSCNMap);
            int minThread = instanceThreadMindedSCNMap.entrySet().stream().min(Comparator.comparingLong(Map.Entry::getValue)).map(Map.Entry::getKey).orElse(0L).intValue();
            long maxScn = instanceThreadMindedSCNMap.values().stream().max(Long::compareTo).orElse(0L);
            boolean checkSkip = true;
            long stopScn = redoLogs.stream().min(Comparator.comparing(RedoLog::getNextChangeScn)).map(RedoLog::getNextChangeScn).orElse(0L);
            resultSet = statement.executeQuery(analyzeLogSql(loopScn - 1));
            //重新跑游标，因为csf只会出现在同一scn中，csf缓存相关的都可以清理
            csfLogContent = null;
//            tapLogger.info("【rac miner】start mining, continuousScn: {} loopScn: {}, stopScn: {}", continuousScn, loopScn, stopScn);
            while (resultSet.next() && isRunning.get()) {
                if (EmptyKit.isNotNull(threadException.get())) {
                    throw new RuntimeException(threadException.get());
                }
                while (ddlStop.get()) {
                    TapSimplify.sleep(1000);
                }
                ResultSetMetaData metaData = resultSet.getMetaData();
                Map<String, Object> logData = JdbcUtil.buildLogData(
                        metaData,
                        resultSet,
                        oracleConfig.getSysZoneId()
                );
                //每次解析日志都更新loopScn，instanceThreadMindedSCNMap
                if (logData.get("SCN") != null && logData.get("SCN") instanceof BigDecimal) {
                    long scn = ((BigDecimal) logData.get("SCN")).longValue();
                    long thread = ((BigDecimal) logData.get("THREAD#")).longValue();
                    if (scn >= stopScn) {
                        break;
                    }
                    loopScn = scn;
                    if (thread > 0) {
                        instanceThreadMindedSCNMap.put(thread, loopScn);
                    } else if (((BigDecimal) logData.get("OPERATION_CODE")).longValue() == 3) {
                        //如果thread为0且是update事件，说明是全null值，需要跳过
                        allNull = true;
                        break;
                    }
                    if (checkSkip) {
                        if (scn > maxScn) {
                            checkSkip = false;
                        } else if (minThread == thread) {
//                            tapLogger.warn("last loop there is some data lost, scn: {}, maxScn: {}, thread: {}", scn, maxScn, thread);
                            checkSkip = false;
                        } else {
                            continue;
                        }
                    }
                }
                logDataProcess(false, logData);
            }
            if (allNull) {
                continuousScn = loopScn;
                //如果全null值，清空redoLogsTemp，重新启动logminer
                redoLogsTemp.clear();
                instanceThreadMindedSCNMap.clear();
                tapLogger.warn("【rac miner】all null value, restart at scn: {}", continuousScn);
                continue;
            }
            //只要存在一个归档日志，就可以取它的next_change#作为下一次的startScn
            if (redoLogs.stream().anyMatch(v -> !v.isOnlineRedo())) {
                continuousScn = stopScn;
                instanceThreadMindedSCNMap.clear();
            }
//            tapLogger.info("【rac miner】stop mining, continuousScn: {} loopScn: {}, stopScn: {}", continuousScn, loopScn, stopScn);
            redoLogsTemp = redoLogs;
        }
    }

    //加载日志文件的SQL拼接
    private String createAddLogMinerSql(Collection<RedoLog> redoLogs) {
        StringBuilder sb = new StringBuilder();
        if (EmptyKit.isNotEmpty(redoLogs)) {
            sb.append("BEGIN");
            int count = 0;
            Set<String> addedRedoLogNames = new HashSet<>();
            for (RedoLog redoLog : redoLogs.stream().filter(v -> EmptyKit.isNull(v.getCreator()) || "ARCH,FGRD".contains(v.getCreator())).collect(Collectors.toList())) {
                String name = redoLog.getName();
                if (!addedRedoLogNames.contains(name)) {
                    if (count == 0) {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.NEW);");
                    } else {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.ADDFILE);");
                    }
                    addedRedoLogNames.add(name);
                    count++;
                }
            }
            sb.append("END;");
        }
        return sb.toString();
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNull(redoLogContent)) {
            return;
        }
        oracleOffset.setLastScn(redoLogContent.getScn());
        //上报引擎offset时，需要将pendingScn设置为未提交事务和continuousScn中的最小值
        long minScn = continuousScn;
        Iterator<LogTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            oracleOffset.setPendingScn(Math.min(iterator.next().getScn(), minScn));
        } else {
            oracleOffset.setPendingScn(minScn);
        }
        oracleOffset.setTimestamp(referenceTime);
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        } else {
            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(referenceTime)), oracleOffset);
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
        super.stopMiner();
    }
}

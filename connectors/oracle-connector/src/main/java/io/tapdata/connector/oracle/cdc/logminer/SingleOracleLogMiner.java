package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.common.cdc.LogTransaction;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.util.JdbcUtil;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.*;

public class SingleOracleLogMiner extends OracleLogMiner {

    private final AtomicLong continuousScn;

    public SingleOracleLogMiner(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws Throwable {
        super(oracleJdbcContext, connectorId, tapLogger);
        continuousScn = new AtomicLong();
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    protected void readyForInit() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
        //挖掘起始scn
        continuousScn.set(oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn());
        tapLogger.info("total start mining scn: " + continuousScn.get());
    }

    @Override
    public void startMiner() throws Throwable {
        setSession();
        RedoLog logFile = checkArchiveAndOnlineLogWithScn(continuousScn.get(), 0L);
        boolean isOnline = logFile.isOnlineRedo();
        boolean reload = true;
        String lastRedoLogContentId = null;
        while (isRunning.get()) {
            if (reload) {
                String addLogMinerSql;
                if (oracleConfig.getDdlSwitch()) {
                    try {
                        addLogMinerSql = createAddLogMinerSqlFirst(logFile);
                        statement.execute(addLogMinerSql);
                        tapLogger.info(ADD_LOG_MINER_SQL_LOG, addLogMinerSql);
                        statement.execute(START_LOG_MINOR_SQL_DDL);
                    } catch (Exception e) {
                        tapLogger.info("for ddl, building new log file...");
                        oracleJdbcContext.execute(STORE_DICT_IN_REDO_SQL);
                        addLogMinerSql = createAddLogMinerSqlFirst(logFile);
                        statement.execute(addLogMinerSql);
                        tapLogger.info(ADD_LOG_MINER_SQL_LOG, addLogMinerSql);
                        statement.execute(START_LOG_MINOR_SQL_DDL);
                    }
                } else {
                    addLogMinerSql = createAddLogMinerSql(logFile);
                    statement.execute(addLogMinerSql);
                    tapLogger.info(ADD_LOG_MINER_SQL_LOG, addLogMinerSql);
                    statement.execute(START_LOG_MINOR_SQL);
                }
                statement.setFetchSize(1000);
            }
            try {
                Map<String, Object> logData = null;
                resultSet = statement.executeQuery(analyzeLogSql(continuousScn.get() - 1));
                boolean maybeDuplicate = true; //重扫时有可能重复，为了精确一次读
                while (resultSet.next() && isRunning.get()) {
                    if (EmptyKit.isNotNull(threadException.get())) {
                        throw new RuntimeException(threadException.get());
                    }
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    logData = JdbcUtil.buildLogData(
                            metaData,
                            resultSet,
                            oracleConfig.getSysZoneId()
                    );
                    if (logData.get("SCN") != null && logData.get("SCN") instanceof BigDecimal) {
                        long scn = ((BigDecimal) logData.get("SCN")).longValue();
                        if (maybeDuplicate && (scn > continuousScn.get() || EmptyKit.isBlank(lastRedoLogContentId))) {
                            maybeDuplicate = false;
                        }
                        continuousScn.set(scn);
                    }
                    if (maybeDuplicate) {
                        //直到扫到上次的精确记录为止
                        if (EmptyKit.isNotBlank(lastRedoLogContentId) && lastRedoLogContentId.equals(generateLogContentId(logData))) {
                            maybeDuplicate = false;
                        }
                        continue;
                    }
                    analyzeLog(logData);
                }
                lastRedoLogContentId = EmptyKit.isNotNull(logData) ? generateLogContentId(logData) : null;
                if (isOnline) {
                    TapSimplify.sleep(oracleConfig.getPollingInterval());
                    logFile = checkArchiveAndOnlineLogWithScn(continuousScn.get(), logFile.getSequence());
                } else {
                    logFile = checkArchiveAndOnlineLogWithScn(continuousScn.get(), logFile.getSequence() + 1);
                }
                reload = !isOnline || !logFile.isOnlineRedo();
                isOnline = logFile.isOnlineRedo();
            } finally {
                EmptyKit.closeQuietly(resultSet);
                if (reload) {
                    ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
                }
            }
        }
    }

    private List<String> getArchivedDicLogs(long timestamp) throws SQLException {
        Timestamp currentTs = new Timestamp(timestamp);
        Timestamp minimalTs = new Timestamp(currentTs.getTime() - (1000 * 60 * 60));
        String currentTsStr = currentTs.toString();
        if (currentTsStr.indexOf(".") >= 0) {
            currentTsStr = currentTsStr.substring(0, currentTsStr.indexOf("."));
        }
        String minimalTsStr = minimalTs.toString();
        if (minimalTsStr.indexOf(".") >= 0) {
            minimalTsStr = minimalTsStr.substring(0, minimalTsStr.indexOf("."));
        }

        String timeClause = "and FIRST_TIME>to_date('" + minimalTsStr + "', 'yyyy-mm-dd hh24:mi:ss')"
                + " and FIRST_TIME<to_date('" + currentTsStr + "', 'yyyy-mm-dd hh24:mi:ss')";

        String sql = String.format(SELECT_ARCHIVE_LOG, timeClause);
        List<String> optionalOracleArchiveLogs = new ArrayList<>();
        oracleJdbcContext.query(sql, resultSet -> {
            while (resultSet.next()) {
                String name = resultSet.getString("NAME");
                optionalOracleArchiveLogs.add(name);
            }
        });
        if (EmptyKit.isEmpty(optionalOracleArchiveLogs)) {
            //5、if necessary store in dictionary
            tapLogger.info("building new log file...");
            oracleJdbcContext.execute(STORE_DICT_IN_REDO_SQL);
            return getArchivedDicLogs(timestamp);
        }
        return optionalOracleArchiveLogs;
    }

    public final static String SELECT_ARCHIVE_LOG = "select *\n" +
            "from (select RECID,\n" +
            "             NAME,\n" +
            "             FIRST_CHANGE#,\n" +
            "             FIRST_TIME,\n" +
            "             NEXT_CHANGE#,\n" +
            "             NEXT_TIME,\n" +
            "             DICTIONARY_BEGIN,\n" +
            "             DICTIONARY_END,\n" +
            "             ROWNUM as num\n" +
            "      from v$archived_log\n" +
            "      where (DICTIONARY_BEGIN = 'YES'\n" +
            "         and DICTIONARY_END = 'YES')\n" +
            " %s " +
            "      order by recid desc)\n" +
            "where ROWNUM<=3";

    private String createAddLogMinerSql(RedoLog redoLog) {
        StringBuilder sb = new StringBuilder();
        if (EmptyKit.isNotNull(redoLog)) {
            sb.append("BEGIN");
            sb.append(ADD_LOG_FILE).append(redoLog.getName()).append("',options=>SYS.dbms_logmnr.NEW);");
            sb.append("END;");
        }
        return sb.toString();
    }

    private String createAddLogMinerSqlFirst(RedoLog redoLog) throws SQLException {
        StringBuilder sb = new StringBuilder();
        if (EmptyKit.isNotNull(redoLog)) {
            sb.append("BEGIN");
            sb.append(ADD_LOG_FILE).append(redoLog.getName()).append("',options=>SYS.dbms_logmnr.NEW);");
            getArchivedDicLogs(redoLog.getFirstTime().getTime()).forEach(logName -> {
                sb.append(ADD_LOG_FILE).append(logName).append("',options=>SYS.dbms_logmnr.ADDFILE);");
            });
            sb.append("END;");
        }
        return sb.toString();
    }

    private RedoLog checkArchiveAndOnlineLogWithScn(Long startScn, Long startSequence) {
        RedoLog logFile = null;
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(SINGLE_LOG_FILE_INIT)
        ) {
            preparedStatement.setLong(1, startScn);
            preparedStatement.setLong(2, startSequence);
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                if (resultSet.next()) {
                    logFile = wrapRedoLog(resultSet);
                }
            }
            TapSimplify.sleep(1000);
            //二次确认
            RedoLog logFileAgain = null;
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                if (resultSet.next()) {
                    logFileAgain = wrapRedoLog(resultSet);
                }
            }
            if (logFile != null && logFile.equals(logFileAgain)) {
                return logFile;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return checkArchiveAndOnlineLogWithScn(startScn, startSequence);
    }

    private RedoLog wrapRedoLog(ResultSet resultSet) throws SQLException {
        RedoLog redoLog = new RedoLog();
        redoLog.setName(resultSet.getString("NAME"));
        redoLog.setFirstChangeScn(resultSet.getLong("FIRST_CHANGE#"));
        //在线的next_change#超过了long的最大值，SQL将next_change#强制为空，代码中为了方便比较next_change#置为Long.MAX_VALUE
        if (EmptyKit.isNull(resultSet.getString("NEXT_CHANGE#"))) {
            redoLog.setNextChangeScn(Long.MAX_VALUE);
        } else {
            redoLog.setNextChangeScn(resultSet.getLong("NEXT_CHANGE#"));
        }
        redoLog.setSequence(resultSet.getLong("SEQUENCE#"));
        redoLog.setOnlineRedo("online".equals(resultSet.getString("STATUS")));
        redoLog.setCreator(resultSet.getString("CREATOR"));
        redoLog.setFirstTime(resultSet.getDate("FIRST_TIME"));
        return redoLog;
    }

    private static final String SINGLE_LOG_FILE_INIT = "select * from\n" +
            "(SELECT 'archived' status, t.NAME, t.sequence#, t.first_change#, t.next_change#, t.creator, t.first_time\n" +
            "from v$archived_log t\n" +
            "where (NAME IS NOT NULL AND DEST_ID != 0 AND STATUS = 'A' AND STANDBY_DEST = 'NO')\n" +
            "  and next_change# >= ? and sequence# >= ?\n" +
            "union ALL\n" +
            "select 'online', lf.MEMBER NAME, l.sequence#, l.first_change#, null, null, l.first_time\n" +
            "FROM v$log l\n" +
            "         LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP#\n" +
            "where l.status = 'CURRENT') order by SEQUENCE#";
    private static final String ADD_LOG_MINER_SQL_LOG = "【single miner】add log miner sql: {}";
    private static final String ADD_LOG_FILE = " SYS.dbms_logmnr.add_logfile(logfilename=>'";

    @Override
    public void stopMiner() throws Throwable {
        ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
        super.stopMiner();
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNull(redoLogContent)) {
            return;
        }
        oracleOffset.setLastScn(redoLogContent.getScn());
        long minScn = continuousScn.get();
        Iterator<LogTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            oracleOffset.setPendingScn(Math.min(iterator.next().getScn(), minScn));
        } else {
            oracleOffset.setPendingScn(minScn);
        }
        oracleOffset.setTimestamp(referenceTime);
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        } else {
            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(referenceTime)), oracleOffset);
        }
    }
}

package io.tapdata.connector.oracle.exception;

import io.tapdata.common.exception.AbstractExceptionCollector;
import io.tapdata.common.exception.ExceptionCollector;
import io.tapdata.exception.*;
import io.tapdata.kit.ErrorKit;
import oracle.jdbc.OracleDatabaseException;

import java.sql.SQLException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OracleExceptionCollector extends AbstractExceptionCollector implements ExceptionCollector {

    private final static String pdkId = "oracle";

    @Override
    protected String getPdkId() {
        return pdkId;
    }

    @Override
    public void collectTerminateByServer(Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 28) {
            throw new TapPdkTerminateByServerEx(pdkId, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectUserPwdInvalid(String username, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1017) {
            throw new TapPdkUserPwdInvalidEx(pdkId, username, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectOffsetInvalid(Object offset, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 8180) {
            throw new TapPdkOffsetOutOfLogEx(pdkId, offset, ErrorKit.getLastCause(cause));
        }
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1291) {
            throw new TapPdkOffsetOutOfLogEx(pdkId, offset, ErrorKit.getLastCause(cause));
        }
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1033) {
            throw new TapPdkOffsetOutOfLogEx(pdkId, offset, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectReadPrivileges(Object operation, List<String> privileges, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1031) {
            throw new TapPdkReadMissingPrivilegesEx(pdkId, operation, privileges, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectWritePrivileges(Object operation, List<String> privileges, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1031) {
            throw new TapPdkWriteMissingPrivilegesEx(pdkId, operation, privileges, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectWriteType(String targetFieldName, String targetFieldType, Object data, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 932) {
            Pattern pattern = Pattern.compile("ORA-00932: (.*) ([A-Z]+), (.*) ([A-Z]+)");
            Matcher matcher = pattern.matcher(ErrorKit.getLastCause(cause).getMessage());
            String fieldType = null;
            if (matcher.find()) {
                fieldType = matcher.group(2);
            }
            throw new TapPdkWriteTypeEx(pdkId, null, fieldType, data, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectWriteLength(String targetFieldName, String targetFieldType, Object data, Throwable cause) {
        //string length
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 12899) {
            Pattern pattern = Pattern.compile("ORA-12899: (.*) \"([\\w\\s]+)\".\"([\\w\\s]+)\".\"([\\w\\s]+)\" .*");
            Matcher matcher = pattern.matcher(ErrorKit.getLastCause(cause).getMessage());
            String fieldName = null;
            if (matcher.find()) {
                fieldName = matcher.group(4);
            }
            throw new TapPdkWriteLengthEx(pdkId, fieldName, null, data, ErrorKit.getLastCause(cause));
        }
        //number length
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1438) {
            throw new TapPdkWriteLengthEx(pdkId, null, null, data, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectViolateUnique(String targetFieldName, Object data, Object constraint, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1) {
            String errorMsg = ErrorKit.getLastCause(cause).getMessage();
            String constraintStr;
            if(errorMsg.contains(")")) {
                constraintStr = errorMsg.substring(errorMsg.indexOf("(") + 1, errorMsg.indexOf(")"));
            } else {
                constraintStr = errorMsg;
            }
            throw new TapPdkViolateUniqueEx(pdkId, targetFieldName, data, constraintStr, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectViolateNull(String targetFieldName, Throwable cause) {
        if (cause instanceof SQLException && ((SQLException) cause).getErrorCode() == 1400) {
            String errorMsg = ErrorKit.getLastCause(cause).getMessage();
            String fieldName = errorMsg.substring(errorMsg.lastIndexOf("\".\"") + 3, errorMsg.lastIndexOf("\""));
            throw new TapPdkViolateNullableEx(pdkId, fieldName, ErrorKit.getLastCause(cause));
        }
    }

    @Override
    public void collectCdcConfigInvalid(Throwable cause) {
        if (cause instanceof SQLException) {
            switch (((SQLException) cause).getErrorCode()) {
                case 1325: //not open archive log
                    throw new TapDbCdcConfigInvalidEx(pdkId,
                            "1、sqlplus / as sysdba\n" +
                                    "2、shutdown immediate\n" +
                                    "3、startup mount\n" +
                                    "4、alter database archivelog\n" +
                                    "5、alter database open",
                            ErrorKit.getLastCause(cause));
                case 0://append error type
            }
        }
        if (cause instanceof OracleDatabaseException) {
            switch (((OracleDatabaseException) cause).getOracleErrorNumber()) {
                case 4036: //PGA memory used by the instance exceeds PGA_AGGREGATE_LIMIT
                    throw new TapPdkRetryableEx(pdkId, ErrorKit.getLastCause(cause));
            }
        }
    }

}

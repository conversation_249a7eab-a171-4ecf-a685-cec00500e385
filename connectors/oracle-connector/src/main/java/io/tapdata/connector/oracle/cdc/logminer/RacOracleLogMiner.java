package io.tapdata.connector.oracle.cdc.logminer;

import io.tapdata.common.cdc.LogTransaction;
import io.tapdata.common.cdc.RedoLogContent;
import io.tapdata.connector.oracle.OracleJdbcContext;
import io.tapdata.connector.oracle.cdc.logminer.bean.RedoLog;
import io.tapdata.connector.oracle.cdc.logminer.bean.ThreadRedoLog;
import io.tapdata.connector.oracle.cdc.logminer.util.JdbcUtil;
import io.tapdata.connector.oracle.cdc.offset.OracleOffset;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.control.HeartbeatEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.exception.TapPdkOffsetOutOfLogEx;
import io.tapdata.exception.TapPdkRetryableEx;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.ErrorKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.END_LOG_MINOR_SQL;
import static io.tapdata.connector.oracle.cdc.logminer.constant.OracleSqlConstant.START_LOG_MINOR_SQL;

@Deprecated
public class RacOracleLogMiner extends OracleLogMiner {

    private final AtomicLong continuousScn; //稳定推进的SCN，兜底不丢数
    private final AtomicLong loopScn; //循环推进的SCN，会根据情况回退
    private final List<ThreadRedoLog> checkLogs; //每次检测日志的线程和最新序列号
    private final Map<String, RedoLog> miningLogs; //正在挖掘的日志文件
    private final Map<String, RedoLog> waitingLogs; //等待挖掘的日志文件
    private final AtomicReference<Exception> exceptionRef = new AtomicReference<>();
    private final ScheduledExecutorService scheduledExecutorService;
    private final AtomicBoolean isOnline = new AtomicBoolean(false); //是否在线连续挖掘，一旦有切归档，该状态会被置false; 如果归档日志一旦挖掘到线上时，该状态会被置true
    private int threadSize; //oracle的rac线程数
    private int reCheckCount = 0; //重新检测次数

    public RacOracleLogMiner(OracleJdbcContext oracleJdbcContext, String connectorId, Log tapLogger) throws Throwable {
        super(oracleJdbcContext, connectorId, tapLogger);
        scheduledExecutorService = new ScheduledThreadPoolExecutor(1);
        continuousScn = new AtomicLong();
        loopScn = new AtomicLong();
        checkLogs = new CopyOnWriteArrayList<>();
        miningLogs = new ConcurrentHashMap<>();
        waitingLogs = new ConcurrentHashMap<>();
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        readyForInit();
    }

    protected void readyForInit() {
        isRunning.set(true);
        initRedoLogQueueAndThread();
        //挖掘起始scn
        continuousScn.set(oracleOffset.getPendingScn() > 0 && oracleOffset.getPendingScn() < oracleOffset.getLastScn() ? oracleOffset.getPendingScn() : oracleOffset.getLastScn());
        tapLogger.info("total start mining scn: " + continuousScn.get());
        //初始化两个日志Map
        checkArchiveAndOnlineLogWithScn(continuousScn.get());
        //生成挖掘定时线程
        scheduledExecutorService.scheduleWithFixedDelay(this::checkArchiveAndOnlineLogWithSequence, 15, 15, TimeUnit.SECONDS);
    }

    private void checkArchiveAndOnlineLogWithScn(Long startScn) {
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(RAC_LOG_FILE_INIT)
        ) {
            preparedStatement.setLong(1, startScn);
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                while (resultSet.next()) {
                    RedoLog redoLog = wrapRedoLog(resultSet);
                    if (redoLog.getFirstChangeScn() <= startScn) {
                        miningLogs.put(redoLog.getThread() + "_" + redoLog.getSequence(), redoLog);
                        threadSize = miningLogs.size();
                    } else {
                        waitingLogs.put(redoLog.getThread() + "_" + redoLog.getSequence(), redoLog);
                    }
                    if (redoLog.isOnlineRedo()) {
                        checkLogs.add(new ThreadRedoLog(redoLog.getThread(), redoLog.getSequence()));
                    }
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    //持续检测最新的归档日志和线上日志（sequence>=old）
    private synchronized void checkArchiveAndOnlineLogWithSequence() {
        String whereClause = checkLogs.stream().map(v -> "(thread#=" + v.getThread() + " and sequence#>=" + v.getSequence() + ")").collect(Collectors.joining(" or "));
        long stopScn = instanceThreadMindedSCNMap.values().stream().min(Long::compareTo).orElse(loopScn.get());
        try (
                Connection connection = oracleJdbcContext.getConnection();
                PreparedStatement preparedStatement = connection.prepareStatement(String.format(RAC_LOG_FILE_CHECK, whereClause))
        ) {
            List<RedoLog> redoLogs = new ArrayList<>();
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                while (resultSet.next()) {
                    redoLogs.add(wrapRedoLog(resultSet));
                }
            }
            TapSimplify.sleep(2000);
            //redoLogsAgain进行二次确认，避免归档频繁时，oracle的日志文件信息不可靠
            List<RedoLog> redoLogsAgain = new ArrayList<>();
            try (
                    ResultSet resultSet = preparedStatement.executeQuery()
            ) {
                while (resultSet.next()) {
                    redoLogsAgain.add(wrapRedoLog(resultSet));
                }
            }
            //只有当两次确认的日志文件信息一致时，才认为是可靠的，否则等下次重新检测
            /*
            特别留意：RedoLog 信息在临界点时，isOnlineRedo 属性不可靠，因此仅判断(RedoLog::isOnlineRedo).count() <= threadSize 还不够
            这里需要增加逻辑，先筛选出必定存在的线上日志，获取线上日志路径，然后通过路径过滤发现数量不超过 threadSize 才是万无一失的
            另外需要防止归档文件暂未生成，online日志又已经切换，导致多份在线文件加载
             */
            if (redoLogs.size() == redoLogsAgain.size() && (validateRedoLogs(redoLogs, threadSize))
                    && redoLogs.stream().allMatch(v -> redoLogsAgain.stream().anyMatch(v2 -> v2.equals(v)))) {
                //checkLogs重新生成，waitingLogs增加新的日志文件
                checkLogs.clear();
                for (RedoLog redoLog : redoLogs) {
                    waitingLogs.put(redoLog.getThread() + "_" + redoLog.getSequence(), redoLog);
                    if (redoLog.isOnlineRedo()) {
                        checkLogs.add(new ThreadRedoLog(redoLog.getThread(), redoLog.getSequence()));
                    }
                }
                //waitingLogs中的日志文件数量大于线程数，说明存在归档日志未被加入挖掘，online状态置为false
                if (waitingLogs.size() > threadSize) {
                    isOnline.set(false);
                } else {
                    //如果waitingLogs中的日志文件数量等于线程数且miningLogs中的日志文件都是online状态，说明持续在挖掘在线日志，每15秒不停地推进continuousScn
                    if (miningLogs.entrySet().stream().filter(v -> v.getValue().isOnlineRedo()).count() == threadSize) {
                        continuousScn.set(stopScn);
                    }
                }
                reCheckCount = 0;
            } else {
                tapLogger.warn("frequent archiving, detection again, redoLogs: {}, redoLogsAgain: {}, checkLogs: {}", redoLogs, redoLogsAgain, checkLogs);
                reCheckCount++;
                if (reCheckCount > 5) {
                    exceptionRef.set(new TapPdkRetryableEx("frequent archiving, reopen log miner", new IllegalStateException("frequent archiving, reopen log miner")));
                }
                checkArchiveAndOnlineLogWithSequence();
            }
        } catch (SQLException e) {
            exceptionRef.set(e);
        }
    }

    public boolean validateRedoLogs(List<RedoLog> redoLogs, int threadSize) {
        if (redoLogs.stream().filter(RedoLog::isOnlineRedo).count() > threadSize) {
            return false;
        }
        String oneOnlineName = redoLogs.stream().filter(RedoLog::isOnlineRedo).findFirst().orElse(new RedoLog()).getName();
        if (EmptyKit.isBlank(oneOnlineName)) {
            return false;
        }
        String onlineRedoLogPath = oneOnlineName.substring(0, oneOnlineName.lastIndexOf("/"));
        if (redoLogs.stream().filter(v -> v.getName().startsWith(onlineRedoLogPath)).count() > threadSize) {
            return false;
        }
        return checkLogs.stream().allMatch(v -> redoLogs.stream().anyMatch(v2 -> v2.getSequence() == v.getSequence() && Objects.equals(v2.getThread(), v.getThread())));
    }

    private RedoLog wrapRedoLog(ResultSet resultSet) throws SQLException {
        RedoLog redoLog = new RedoLog();
        redoLog.setName(resultSet.getString("NAME"));
        redoLog.setFirstChangeScn(resultSet.getLong("FIRST_CHANGE#"));
        //在线的next_change#超过了long的最大值，SQL将next_change#强制为空，代码中为了方便比较next_change#置为Long.MAX_VALUE
        if (EmptyKit.isNull(resultSet.getString("NEXT_CHANGE#"))) {
            redoLog.setNextChangeScn(Long.MAX_VALUE);
        } else {
            redoLog.setNextChangeScn(resultSet.getLong("NEXT_CHANGE#"));
        }
        redoLog.setSequence(resultSet.getLong("SEQUENCE#"));
        redoLog.setThread(resultSet.getLong("THREAD#"));
        redoLog.setOnlineRedo("online".equals(resultSet.getString("STATUS")));
        redoLog.setCreator(resultSet.getString("CREATOR"));
        return redoLog;
    }

    private static final String RAC_LOG_FILE_INIT = "SELECT 'archived' status, t.NAME, t.THREAD#, t.sequence#, t.first_change#, t.next_change#, t.creator\n" +
            "from v$archived_log t\n" +
            "where (NAME IS NOT NULL AND DEST_ID != 0 AND STATUS = 'A' AND STANDBY_DEST = 'NO')\n" +
            "  and next_change# >= ?\n" +
            "union ALL\n" +
            "select 'online', lf.MEMBER NAME, l.THREAD#, l.sequence#, l.first_change#, null, null\n" +
            "FROM v$log l\n" +
            "         LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP#\n" +
            "where l.status = 'CURRENT'";

    private static final String RAC_LOG_FILE_CHECK = "select * from (SELECT 'archived' status,t.NAME,t.THREAD#,t.sequence#,t.first_change#,t.next_change#,t.creator\n" +
            "from v$archived_log t where (NAME IS NOT NULL AND DEST_ID !=0 AND STATUS='A' AND STANDBY_DEST='NO')\n" +
            "union ALL \n" +
            "select 'online',lf.MEMBER NAME,l.THREAD#,l.sequence#,l.first_change#,null,null\n" +
            "FROM v$log l LEFT JOIN v$logfile lf ON l.GROUP# = lf.GROUP# where l.status='CURRENT') logs\n" +
            "where %s";

    @Override
    public void startMiner() throws Throwable {
        setSession();
        boolean reload = true;
        loopScn.set(continuousScn.get());
        //不停地拉取信号，挖掘日志
        while (isRunning.get()) {
            if (EmptyKit.isNotNull(exceptionRef.get())) {
                throw exceptionRef.get();
            }
            if (reload) {
                cacheRedoLogContent.clear();
                String addLogMinerSql = createAddLogMinerSql(miningLogs.values());
                if (EmptyKit.isBlank(addLogMinerSql)) {
                    throw new TapPdkOffsetOutOfLogEx("oracle", oracleOffset, new RuntimeException("redo log files has been cleared, please check"));
                }
                statement.execute(addLogMinerSql);
                tapLogger.info("【rac miner】add log miner sql: {}", addLogMinerSql);
                statement.execute(START_LOG_MINOR_SQL);
                statement.setFetchSize(1000);
            }
            try {
                long startMindScn = instanceThreadMindedSCNMap.values().stream().min(Long::compareTo).orElse(loopScn.get());
                resultSet = statement.executeQuery(analyzeLogSql(startMindScn - 1));
                //重新跑游标，因为csf只会出现在同一scn中，csf缓存相关的都可以清理
                csfLogContent = null;
                while (resultSet.next() && isRunning.get()) {
                    if (EmptyKit.isNotNull(exceptionRef.get())) {
                        throw exceptionRef.get();
                    }
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    Map<String, Object> logData = JdbcUtil.buildLogData(
                            metaData,
                            resultSet,
                            oracleConfig.getSysZoneId()
                    );
                    //每次解析日志都更新loopScn，instanceThreadMindedSCNMap
                    if (logData.get("SCN") != null && logData.get("SCN") instanceof BigDecimal) {
                        long scn = ((BigDecimal) logData.get("SCN")).longValue();
                        if (scn != loopScn.get()) {
                            cacheRedoLogContent.clear();
                        }
                        loopScn.set(scn);
                        instanceThreadMindedSCNMap.put(((BigDecimal) logData.get("THREAD#")).longValue(), loopScn.get());
                    }
                    logDataProcess(true, logData);
                    //在线状态，正在挖掘日志文件数和线程数相等，表示正在线上持续挖掘，不需要重载文件。否则清理不再需要的日志文件，重新载入
                    if (isOnline.get()) {
                        if (miningLogs.size() > threadSize) {
                            if (clearOldLogs()) {
                                reload = true;
                                break;
                            }
                        } else {
                            reload = false;
                        }
                    } else {
                        //等待挖掘日志文件中如果有firstChangeScn已将近loopScn，先将文件加载，需要注意的是online日志持续挖掘，但waitingLogs有归档日志还未加入挖掘，需要除外
                        List<RedoLog> diff = waitingLogs.values().stream().filter(v -> miningLogs.values().stream().noneMatch(log -> log.equals(v))).collect(Collectors.toList());
                        if (diff.stream().anyMatch(v -> v.getFirstChangeScn() <= loopScn.get() + 1000)) {
                            //先清理不需要的miningLogs
                            tapLogger.info("【debug before】miningLogs: {}", miningLogs);
                            tapLogger.info("【debug before】waitingLogs: {}", waitingLogs);
                            tapLogger.info("【debug before】loopScn: {}", loopScn.get());
                            tapLogger.info("【debug before】continuousScn: {}", continuousScn.get());
                            clearOldLogs();
                            //一旦出现归档后需要从最新文件的最小firstChangeScn重新挖掘
                            long restartScn = diff.stream().mapToLong(RedoLog::getFirstChangeScn).min().orElse(loopScn.get() + 1000) - 1000;
                            //waitingLogs将满足条件的日志文件加入miningLogs，如果是归档日志，需要在waitingLogs去除，不满足条件的日志文件暂时先从miningLogs去除
                            Iterator<Map.Entry<String, RedoLog>> iterator = waitingLogs.entrySet().iterator();
                            while (iterator.hasNext()) {
                                RedoLog redoLog = iterator.next().getValue();
                                if (redoLog.getFirstChangeScn() <= loopScn.get() + 1000) {
                                    miningLogs.put(redoLog.getThread() + "_" + redoLog.getSequence(), redoLog);
                                    if (!redoLog.isOnlineRedo()) {
                                        iterator.remove();
                                    }
                                } else {
                                    miningLogs.remove(redoLog.getThread() + "_" + redoLog.getSequence());
                                }
                            }
                            //miningLogs中一旦有一个处于online状态，表示已切换到线上持续挖掘，将其余在线日志也加载进来，并将online状态置为true，通过定时线程检测去打点推进continuousScn；
                            //否则将continuousScn按归档restartScn推进
                            if (miningLogs.entrySet().stream().anyMatch(v -> v.getValue().isOnlineRedo())) {
                                waitingLogs.values().stream().filter(RedoLog::isOnlineRedo).forEach(v -> miningLogs.put(v.getThread() + "_" + v.getSequence(), v));
                                isOnline.set(true);
                            } else {
                                continuousScn.set(Math.max(restartScn, continuousScn.get()));
                            }
                            //每次出现重载，loopScn需要与continuousScn同步，清空instanceThreadMindedSCNMap
                            loopScn.set(continuousScn.get());
                            instanceThreadMindedSCNMap.clear();
                            reload = true;
                            tapLogger.info("【debug after】miningLogs: {}", miningLogs);
                            tapLogger.info("【debug after】waitingLogs: {}", waitingLogs);
                            tapLogger.info("【debug after】loopScn: {}", loopScn.get());
                            tapLogger.info("【debug after】continuousScn: {}", continuousScn.get());
                            break;
                        } else {
                            clearOldLogs();
                            reload = false;
                        }
                    }
                }
            } finally {
                EmptyKit.closeQuietly(resultSet);
                if (reload) {
                    ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
                }
            }
        }
    }

    private boolean clearOldLogs() {
        List<String> removeLogs = miningLogs.values().stream().filter(v -> v.getNextChangeScn() < continuousScn.get()).map(v -> v.getThread() + "_" + v.getSequence()).collect(Collectors.toList());
        if (EmptyKit.isNotEmpty(removeLogs)) {
            tapLogger.info("【rac miner】remove log miner sql: {}", removeLogs);
            removeLogs.forEach(miningLogs::remove);
            return true;
        }
        return false;
    }

    //加载日志文件的SQL拼接
    private String createAddLogMinerSql(Collection<RedoLog> redoLogs) {
        StringBuilder sb = new StringBuilder();
        if (EmptyKit.isNotEmpty(redoLogs)) {
            sb.append("BEGIN");
            int count = 0;
            Set<String> addedRedoLogNames = new HashSet<>();
            for (RedoLog redoLog : redoLogs.stream().filter(v -> EmptyKit.isNull(v.getCreator()) || "ARCH,FGRD".contains(v.getCreator())).collect(Collectors.toList())) {
                String name = redoLog.getName();
                if (!addedRedoLogNames.contains(name)) {
                    if (count == 0) {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.NEW);");
                    } else {
                        sb.append(" SYS.dbms_logmnr.add_logfile(logfilename=>'").append(name).append("',options=>SYS.dbms_logmnr.ADDFILE);");
                    }
                    addedRedoLogNames.add(name);
                    count++;
                }
            }
            sb.append("END;");
        }
        return sb.toString();
    }

    @Override
    protected void submitEvent(RedoLogContent redoLogContent, List<TapEvent> eventList) {
        OracleOffset oracleOffset = new OracleOffset();
        if (EmptyKit.isNull(redoLogContent)) {
            return;
        }
        oracleOffset.setLastScn(redoLogContent.getScn());
        //上报引擎offset时，需要将pendingScn设置为未提交事务和continuousScn中的最小值
        long minScn = continuousScn.get();
        Iterator<LogTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            oracleOffset.setPendingScn(Math.min(iterator.next().getScn(), minScn));
        } else {
            oracleOffset.setPendingScn(minScn);
        }
        oracleOffset.setTimestamp(referenceTime);
        if (eventList.size() > 0) {
            consumer.accept(eventList, oracleOffset);
        } else {
            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(referenceTime)), oracleOffset);
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        ErrorKit.ignoreAnyError(() -> statement.execute(END_LOG_MINOR_SQL));
        ErrorKit.ignoreAnyError(scheduledExecutorService::shutdown);
        super.stopMiner();
    }

    protected RedoLogContent wrapRedoLogContent(Object logData) throws SQLException {
        if (csfLogContent == null) {
            return buildRedoLogContent(logData);
        } else {
            if (logData instanceof Map) {
                if (!RedoLogContent.id(
                        ((BigDecimal) ((Map) logData).get("THREAD#")).intValue(),
                        (String) ((Map) logData).get("XID"),
                        ((BigDecimal) ((Map) logData).get("SCN")).longValue(),
                        (String) ((Map) logData).get("RS_ID"),
                        ((BigDecimal) ((Map) logData).get("SSN")).longValue(), 0, "").equals(
                        RedoLogContent.id(
                                csfLogContent.getThread(),
                                csfLogContent.getXid(),
                                csfLogContent.getScn(),
                                csfLogContent.getRsId(),
                                csfLogContent.getSsn(), 0, ""))) {
                    tapLogger.warn("csf log content has been dropped, {}", csfLogContent);
                    csfLogContent = null;
                    return buildRedoLogContent(logData);
                }
            }
            return appendRedoAndUndoSql(logData);
        }
    }
}

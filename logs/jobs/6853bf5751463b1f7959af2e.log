[TRACE] 2025-06-19 15:43:12.060 - [任务 20] - Task initialization... 
[TRACE] 2025-06-19 15:43:12.140 - [任务 20] - Start task milestones: 6853bf5751463b1f7959af2e(任务 20) 
[INFO ] 2025-06-19 15:43:12.141 - [任务 20] - Loading table structure completed 
[TRACE] 2025-06-19 15:43:12.198 - [任务 20] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:43:12.198 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:43:12.227 - [任务 20] - Task started 
[TRACE] 2025-06-19 15:43:12.227 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:12.227 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:12.227 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:43:12.227 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:43:12.677 - [任务 20][dummy_sqlserver] - Source connector(dummy_sqlserver) initialization completed 
[TRACE] 2025-06-19 15:43:12.677 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" read batch size: 100 
[TRACE] 2025-06-19 15:43:12.677 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" event queue capacity: 200 
[TRACE] 2025-06-19 15:43:12.677 - [任务 20][dummy_sqlserver] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 15:43:12.720 - [任务 20][dummy_sqlserver] - Use existing stream offset: {"syncStage":null,"beginTimes":1750318992677,"lastTimes":1750318992677,"lastTN":0,"tableStats":{}} 
[INFO ] 2025-06-19 15:43:12.721 - [任务 20][dummy_sqlserver] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 15:43:12.723 - [任务 20][dummy_sqlserver] - Initial sync started 
[INFO ] 2025-06-19 15:43:12.723 - [任务 20][dummy_sqlserver] - Starting batch read from table: dummy_test 
[TRACE] 2025-06-19 15:43:12.723 - [任务 20][dummy_sqlserver] - Query snapshot row size completed: dummy_sqlserver(16c486b2-737a-4560-ad43-691d6322b986) 
[TRACE] 2025-06-19 15:43:12.723 - [任务 20][dummy_sqlserver] - Table dummy_test is going to be initial synced 
[INFO ] 2025-06-19 15:43:12.724 - [任务 20][dummy_sqlserver] - Start dummy_test batch read 
[INFO ] 2025-06-19 15:43:13.028 - [任务 20][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:43:13.029 - [任务 20][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:43:13.029 - [任务 20][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 15:43:13.231 - [任务 20][sqlserver_ad] - Apply table structure to target database 
[INFO ] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Compile dummy_test batch read 
[INFO ] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Table dummy_test has been completed batch read 
[TRACE] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Initial sync completed 
[INFO ] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Batch read completed. 
[TRACE] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Incremental sync starting... 
[TRACE] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Initial sync completed 
[TRACE] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1750318992677,"lastTimes":1750318992677,"lastTN":0,"tableStats":{}} 
[INFO ] 2025-06-19 15:43:13.423 - [任务 20][dummy_sqlserver] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:43:13.424 - [任务 20][dummy_sqlserver] - Start [dummy_test] stream read 
[TRACE] 2025-06-19 15:43:13.629 - [任务 20][dummy_sqlserver] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 15:43:14.978 - [任务 20][sqlserver_ad] - Process after table "dummy_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 15:43:14.978 - [任务 20][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-19 15:43:29.912 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] running status set to false 
[INFO ] 2025-06-19 15:43:29.912 - [任务 20][dummy_sqlserver] - Stop connector 
[TRACE] 2025-06-19 15:43:29.913 - [任务 20][dummy_sqlserver] - Incremental sync completed 
[TRACE] 2025-06-19 15:43:29.915 - [任务 20][dummy_sqlserver] - PDK connector node stopped: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750318992595 
[TRACE] 2025-06-19 15:43:29.915 - [任务 20][dummy_sqlserver] - PDK connector node released: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750318992595 
[TRACE] 2025-06-19 15:43:29.915 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] schema data cleaned 
[TRACE] 2025-06-19 15:43:29.915 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] monitor closed 
[TRACE] 2025-06-19 15:43:29.915 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] close complete, cost 4 ms 
[TRACE] 2025-06-19 15:43:29.926 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] running status set to false 
[INFO ] 2025-06-19 15:43:29.927 - [任务 20][sqlserver_ad] - Retry operation null failed, total cost 07:43:29.926000 
[TRACE] 2025-06-19 15:43:29.928 - [任务 20][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750318992558 
[TRACE] 2025-06-19 15:43:29.928 - [任务 20][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750318992558 
[TRACE] 2025-06-19 15:43:29.928 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] schema data cleaned 
[TRACE] 2025-06-19 15:43:29.928 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] monitor closed 
[TRACE] 2025-06-19 15:43:29.935 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] close complete, cost 12 ms 
[TRACE] 2025-06-19 15:43:29.935 - [任务 20][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test 
[WARN ] 2025-06-19 15:43:29.935 - [任务 20][sqlserver_ad] - Save error event failed: Failed to call rest api, msg no exception. 
[ERROR] 2025-06-19 15:43:29.935 - [任务 20][sqlserver_ad] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:820)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:919)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:885)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:858)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:811)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:701)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:832)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:779)
	... 5 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:993)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:907)
	... 12 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.net.SocketException: Socket closed
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:167)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:92)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:55)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 28 more
Caused by: java.net.SocketException: Socket closed
	at java.base/sun.nio.ch.NioSocketImpl.endRead(NioSocketImpl.java:253)
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:332)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:976)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:134)
	at io.tapdata.common.dml.NormalWriteRecorder.addAndCheckCommit(NormalWriteRecorder.java:174)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:137)
	... 33 more

[TRACE] 2025-06-19 15:43:38.037 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:43:38.858 - [任务 20] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@6c611ea3 
[TRACE] 2025-06-19 15:43:38.858 - [任务 20] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3d59bdc2 
[TRACE] 2025-06-19 15:43:38.979 - [任务 20] - Stop task milestones: 6853bf5751463b1f7959af2e(任务 20)  
[TRACE] 2025-06-19 15:43:38.980 - [任务 20] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:43:38.980 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:43:38.980 - [任务 20] - Task stopped. 
[TRACE] 2025-06-19 15:43:39.023 - [任务 20] - Remove memory task client succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 15:43:39.023 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 15:44:15.030 - [任务 20] - Task initialization... 
[TRACE] 2025-06-19 15:44:15.031 - [任务 20] - Start task milestones: 6853bf5751463b1f7959af2e(任务 20) 
[INFO ] 2025-06-19 15:44:15.126 - [任务 20] - Loading table structure completed 
[TRACE] 2025-06-19 15:44:15.126 - [任务 20] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:44:15.160 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:44:15.160 - [任务 20] - Task started 
[TRACE] 2025-06-19 15:44:15.170 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:44:15.170 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:44:15.170 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:44:15.170 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:44:15.275 - [任务 20][dummy_sqlserver] - Source connector(dummy_sqlserver) initialization completed 
[TRACE] 2025-06-19 15:44:15.275 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" read batch size: 100 
[TRACE] 2025-06-19 15:44:15.275 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" event queue capacity: 200 
[INFO ] 2025-06-19 15:44:15.275 - [任务 20][dummy_sqlserver] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 15:44:15.278 - [任务 20][dummy_sqlserver] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750319009495,"lastTN":18000,"tableStats":{"dummy_test":{"insertTotals":17000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 15:44:15.318 - [任务 20][dummy_sqlserver] - Batch read completed. 
[TRACE] 2025-06-19 15:44:15.318 - [任务 20][dummy_sqlserver] - Incremental sync starting... 
[TRACE] 2025-06-19 15:44:15.318 - [任务 20][dummy_sqlserver] - Initial sync completed 
[TRACE] 2025-06-19 15:44:15.318 - [任务 20][dummy_sqlserver] - Starting stream read, table list: [dummy_test], offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750319009495,"lastTN":18000,"tableStats":{"dummy_test":{"insertTotals":17000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 15:44:15.319 - [任务 20][dummy_sqlserver] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:44:15.319 - [任务 20][dummy_sqlserver] - Start [dummy_test] stream read 
[TRACE] 2025-06-19 15:44:15.319 - [任务 20][dummy_sqlserver] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2025-06-19 15:44:15.704 - [任务 20][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:44:15.705 - [任务 20][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:44:15.705 - [任务 20][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 15:44:15.723 - [任务 20][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-19 15:45:22.474 - [任务 20][sqlserver_ad] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 596, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 无法继续执行，因为会话处于终止状态。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 15:45:22.474 - [任务 20][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD, retry times 1/10, first retry time 2025-06-19 15:45:22, next retry time 2025-06-19 15:46:22 
[TRACE] 2025-06-19 15:46:24.137 - [任务 20][sqlserver_ad] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2025-06-19 15:46:24.139 - [任务 20][sqlserver_ad] - Retry operation TARGET_WRITE_RECORD success, total cost 00:01:01.664000 
[TRACE] 2025-06-19 15:47:00.018 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] running status set to false 
[INFO ] 2025-06-19 15:47:00.018 - [任务 20][dummy_sqlserver] - Stop connector 
[INFO ] 2025-06-19 15:47:00.022 - [任务 20][dummy_sqlserver] - Compile [dummy_test] batch read 
[TRACE] 2025-06-19 15:47:00.022 - [任务 20][dummy_sqlserver] - Incremental sync completed 
[TRACE] 2025-06-19 15:47:00.022 - [任务 20][dummy_sqlserver] - PDK connector node stopped: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750319055186 
[TRACE] 2025-06-19 15:47:00.023 - [任务 20][dummy_sqlserver] - PDK connector node released: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750319055186 
[TRACE] 2025-06-19 15:47:00.023 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] schema data cleaned 
[TRACE] 2025-06-19 15:47:00.023 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] monitor closed 
[TRACE] 2025-06-19 15:47:00.024 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] close complete, cost 6 ms 
[TRACE] 2025-06-19 15:47:00.024 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] running status set to false 
[INFO ] 2025-06-19 15:47:00.043 - [任务 20][sqlserver_ad] - Retry operation null failed, total cost 07:47:00.040000 
[TRACE] 2025-06-19 15:47:00.043 - [任务 20][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: dummy_test 
[WARN ] 2025-06-19 15:47:00.044 - [任务 20][sqlserver_ad] - Save error event failed: Failed to call rest api, msg no exception. 
[TRACE] 2025-06-19 15:47:00.044 - [任务 20][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750319055190 
[TRACE] 2025-06-19 15:47:00.044 - [任务 20][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750319055190 
[TRACE] 2025-06-19 15:47:00.044 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] schema data cleaned 
[TRACE] 2025-06-19 15:47:00.044 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] monitor closed 
[TRACE] 2025-06-19 15:47:00.249 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] close complete, cost 20 ms 
[TRACE] 2025-06-19 15:47:09.159 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:47:10.164 - [任务 20] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@22cfbabc 
[TRACE] 2025-06-19 15:47:10.165 - [任务 20] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6d621417 
[TRACE] 2025-06-19 15:47:10.279 - [任务 20] - Stop task milestones: 6853bf5751463b1f7959af2e(任务 20)  
[TRACE] 2025-06-19 15:47:10.280 - [任务 20] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:47:10.280 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:47:10.280 - [任务 20] - Task stopped. 
[TRACE] 2025-06-19 15:47:10.318 - [任务 20] - Remove memory task client succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 15:47:10.318 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 16:29:04.018 - [任务 20] - Task initialization... 
[TRACE] 2025-06-19 16:29:04.022 - [任务 20] - Start task milestones: 6853bf5751463b1f7959af2e(任务 20) 
[INFO ] 2025-06-19 16:29:04.124 - [任务 20] - Loading table structure completed 
[TRACE] 2025-06-19 16:29:04.124 - [任务 20] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 16:29:04.176 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 16:29:04.176 - [任务 20] - Task started 
[TRACE] 2025-06-19 16:29:04.190 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:04.190 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:04.190 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 16:29:04.190 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 16:29:04.298 - [任务 20][dummy_sqlserver] - Source connector(dummy_sqlserver) initialization completed 
[TRACE] 2025-06-19 16:29:04.298 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" read batch size: 100 
[TRACE] 2025-06-19 16:29:04.298 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" event queue capacity: 200 
[INFO ] 2025-06-19 16:29:04.299 - [任务 20][dummy_sqlserver] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750319218736,"lastTN":83900,"tableStats":{"dummy_test":{"insertTotals":100900,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Batch read completed. 
[TRACE] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Incremental sync starting... 
[TRACE] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Initial sync completed 
[TRACE] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Starting stream read, table list: [dummy_test], offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750319218736,"lastTN":83900,"tableStats":{"dummy_test":{"insertTotals":100900,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Start [dummy_test] stream read 
[TRACE] 2025-06-19 16:29:04.340 - [任务 20][dummy_sqlserver] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2025-06-19 16:29:04.559 - [任务 20][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 16:29:04.559 - [任务 20][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 16:29:04.559 - [任务 20][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 16:29:04.573 - [任务 20][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-19 16:29:35.146 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] running status set to false 
[INFO ] 2025-06-19 16:29:35.147 - [任务 20][dummy_sqlserver] - Stop connector 
[INFO ] 2025-06-19 16:29:35.148 - [任务 20][dummy_sqlserver] - Compile [dummy_test] batch read 
[TRACE] 2025-06-19 16:29:35.148 - [任务 20][dummy_sqlserver] - Incremental sync completed 
[TRACE] 2025-06-19 16:29:35.153 - [任务 20][dummy_sqlserver] - PDK connector node stopped: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750321744209 
[TRACE] 2025-06-19 16:29:35.153 - [任务 20][dummy_sqlserver] - PDK connector node released: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750321744209 
[TRACE] 2025-06-19 16:29:35.153 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] schema data cleaned 
[TRACE] 2025-06-19 16:29:35.153 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] monitor closed 
[TRACE] 2025-06-19 16:29:35.154 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] close complete, cost 9 ms 
[TRACE] 2025-06-19 16:29:35.154 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] running status set to false 
[TRACE] 2025-06-19 16:29:35.164 - [任务 20][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750321744213 
[TRACE] 2025-06-19 16:29:35.165 - [任务 20][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750321744213 
[TRACE] 2025-06-19 16:29:35.165 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] schema data cleaned 
[TRACE] 2025-06-19 16:29:35.165 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] monitor closed 
[TRACE] 2025-06-19 16:29:35.366 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] close complete, cost 11 ms 
[TRACE] 2025-06-19 16:29:44.231 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:29:45.237 - [任务 20] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@5cbdc84f 
[TRACE] 2025-06-19 16:29:45.239 - [任务 20] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c830f0f 
[TRACE] 2025-06-19 16:29:45.239 - [任务 20] - Stop task milestones: 6853bf5751463b1f7959af2e(任务 20)  
[TRACE] 2025-06-19 16:29:45.357 - [任务 20] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:29:45.358 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:29:45.358 - [任务 20] - Task stopped. 
[TRACE] 2025-06-19 16:29:45.387 - [任务 20] - Remove memory task client succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 16:29:45.387 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 16:29:46.907 - [任务 20] - Task initialization... 
[TRACE] 2025-06-19 16:29:46.958 - [任务 20] - Start task milestones: 6853bf5751463b1f7959af2e(任务 20) 
[INFO ] 2025-06-19 16:29:46.958 - [任务 20] - Loading table structure completed 
[TRACE] 2025-06-19 16:29:47.028 - [任务 20] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 16:29:47.029 - [任务 20] - The engine receives 任务 20 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 16:29:47.045 - [任务 20] - Task started 
[TRACE] 2025-06-19 16:29:47.045 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:47.045 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 16:29:47.056 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:47.056 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 16:29:47.160 - [任务 20][dummy_sqlserver] - Source connector(dummy_sqlserver) initialization completed 
[TRACE] 2025-06-19 16:29:47.160 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" read batch size: 100 
[TRACE] 2025-06-19 16:29:47.160 - [任务 20][dummy_sqlserver] - Source node "dummy_sqlserver" event queue capacity: 200 
[INFO ] 2025-06-19 16:29:47.160 - [任务 20][dummy_sqlserver] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750321773141,"lastTN":28400,"tableStats":{"dummy_test":{"insertTotals":129300,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Batch read completed. 
[TRACE] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Incremental sync starting... 
[TRACE] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Initial sync completed 
[TRACE] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Starting stream read, table list: [dummy_test], offset: {"syncStage":"Incremental","beginTimes":1750318992677,"lastTimes":1750321773141,"lastTN":28400,"tableStats":{"dummy_test":{"insertTotals":129300,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 16:29:47.197 - [任务 20][dummy_sqlserver] - Start [dummy_test] stream read 
[TRACE] 2025-06-19 16:29:47.400 - [任务 20][dummy_sqlserver] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2025-06-19 16:29:47.513 - [任务 20][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 16:29:47.513 - [任务 20][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 16:29:47.513 - [任务 20][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 16:29:47.714 - [任务 20][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-19 16:30:22.368 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] running status set to false 
[INFO ] 2025-06-19 16:30:22.369 - [任务 20][dummy_sqlserver] - Stop connector 
[INFO ] 2025-06-19 16:30:22.369 - [任务 20][dummy_sqlserver] - Compile [dummy_test] batch read 
[TRACE] 2025-06-19 16:30:22.369 - [任务 20][dummy_sqlserver] - Incremental sync completed 
[TRACE] 2025-06-19 16:30:22.375 - [任务 20][dummy_sqlserver] - PDK connector node stopped: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750321787074 
[TRACE] 2025-06-19 16:30:22.379 - [任务 20][dummy_sqlserver] - PDK connector node released: HazelcastSourcePdkDataNode_16c486b2-737a-4560-ad43-691d6322b986_1750321787074 
[TRACE] 2025-06-19 16:30:22.379 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] schema data cleaned 
[TRACE] 2025-06-19 16:30:22.379 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] monitor closed 
[TRACE] 2025-06-19 16:30:22.379 - [任务 20][dummy_sqlserver] - Node dummy_sqlserver[16c486b2-737a-4560-ad43-691d6322b986] close complete, cost 9 ms 
[TRACE] 2025-06-19 16:30:22.379 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] running status set to false 
[TRACE] 2025-06-19 16:30:22.396 - [任务 20][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750321787064 
[TRACE] 2025-06-19 16:30:22.398 - [任务 20][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_08c79f2a-5750-41d4-bd00-beeadbb55545_1750321787064 
[TRACE] 2025-06-19 16:30:22.398 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] schema data cleaned 
[TRACE] 2025-06-19 16:30:22.399 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] monitor closed 
[TRACE] 2025-06-19 16:30:22.399 - [任务 20][sqlserver_ad] - Node sqlserver_ad[08c79f2a-5750-41d4-bd00-beeadbb55545] close complete, cost 20 ms 
[TRACE] 2025-06-19 16:30:31.705 - [任务 20] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:30:32.634 - [任务 20] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@246307e8 
[TRACE] 2025-06-19 16:30:32.634 - [任务 20] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7513aed1 
[TRACE] 2025-06-19 16:30:32.749 - [任务 20] - Stop task milestones: 6853bf5751463b1f7959af2e(任务 20)  
[TRACE] 2025-06-19 16:30:32.749 - [任务 20] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:30:32.749 - [任务 20] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:30:32.749 - [任务 20] - Task stopped. 
[TRACE] 2025-06-19 16:30:32.792 - [任务 20] - Remove memory task client succeed, task: 任务 20[6853bf5751463b1f7959af2e] 
[TRACE] 2025-06-19 16:30:32.792 - [任务 20] - Destroy memory task client cache succeed, task: 任务 20[6853bf5751463b1f7959af2e] 

[TRACE] 2025-06-19 14:10:21.089 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:10:21.174 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:10:21.175 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:10:21.241 - [任务 19] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:10:21.241 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:10:21.265 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:10:21.471 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:10:21.860 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:10:21.860 - [任务 19][local_pg] - The table SampleTable has already exist. 
[INFO ] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:10:21.993 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:10:21.993 - [任务 19][sqlserver_ad] - building CT table for table SampleTable 
[INFO ] 2025-06-19 14:10:29.365 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.365 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:10:29.368 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:10:29.369 - [任务 19][sqlserver_ad] - Starting batch read from table: SampleTable 
[TRACE] 2025-06-19 14:10:29.447 - [任务 19][sqlserver_ad] - Table SampleTable is going to be initial synced 
[TRACE] 2025-06-19 14:10:29.447 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[INFO ] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Table SampleTable has been completed batch read 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:10:29.557 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SampleTable], offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.557 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:10:29.762 - [任务 19][sqlserver_ad] - opened cdc tables: [SampleTable] 
[WARN ] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6853a9d50c793fc2478cc85d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[04a3b729-701a-4cf5-93ec-d22f55ff7388], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Process after table "SampleTable" initial sync finished, cost: 17 ms 
[INFO ] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:10:30.384 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:10:30.384 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SampleTable], data change syncing 
[TRACE] 2025-06-19 14:10:47.102 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 14:10:47.305 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 14:10:50.111 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.111 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.112 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.112 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:10:50.113 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:10:50.113 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.122 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:10:50.122 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 9 ms 
[TRACE] 2025-06-19 14:10:58.878 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:10:59.884 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@38959f83 
[TRACE] 2025-06-19 14:10:59.887 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@405e29c3 
[TRACE] 2025-06-19 14:10:59.887 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 14:11:00.021 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:11:00.021 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:11:00.021 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 14:11:00.065 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:11:00.068 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:13:52.677 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:13:52.732 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:13:52.732 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:13:52.818 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:13:52.818 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:13:52.834 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:13:53.040 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:13:53.239 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:13:53.418 - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:13:53.546 - [任务 19][sqlserver_ad] - building CT table for table SourceOfRegion 
[INFO ] 2025-06-19 14:13:53.845 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:13:54.086 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:13:54.087 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:13:54.097 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:13:54.097 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:13:54.109 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:13:54.109 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:13:54.118 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:13:54.118 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:13:54.127 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:13:54.127 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:13:54.138 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:13:54.138 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:13:54.339 - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:13:54.703 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:13:54.913 - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:13:54.914 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:13:54.928 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:13:54.928 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:15:36.397 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 14:15:37.207 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:15:39.420 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[TRACE] 2025-06-19 14:15:46.503 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:15:47.462 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@12e5e795 
[TRACE] 2025-06-19 14:15:47.462 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23e9bf10 
[TRACE] 2025-06-19 14:15:47.584 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 14:15:47.584 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:15:47.585 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:15:47.585 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 14:15:47.626 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:15:47.629 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:46:39.066 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:46:39.066 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:46:39.163 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:46:39.166 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:46:39.213 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:46:39.213 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:46:39.646 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:46:39.851 - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:46:39.895 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:46:40.127 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:46:40.278 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:46:40.364 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:46:40.364 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:46:40.373 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:46:40.373 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:46:40.380 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:46:40.380 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:46:40.388 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:46:40.388 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:46:40.396 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:46:40.396 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:46:40.400 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:46:40.400 - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:46:40.765 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.765 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:46:40.965 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:46:41.165 - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:46:41.166 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:46:41.168 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:46:41.370 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[WARN ] 2025-06-19 14:48:09.148 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 14:48:09.234 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 14:48:09, next retry time 2025-06-19 14:49:09 
[INFO ] 2025-06-19 14:49:09.722 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 14:49:09.789 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:49:09.792 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 14:49:09.792 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.625000 
[TRACE] 2025-06-19 14:49:09.995 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 15:30:14.779 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 15:30:14.982 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:30:18.005 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[TRACE] 2025-06-19 15:30:25.105 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:30:25.993 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@65fd2f7c 
[TRACE] 2025-06-19 15:30:25.993 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@545248ab 
[TRACE] 2025-06-19 15:30:26.114 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 15:30:26.115 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:30:26.115 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:30:26.168 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 15:30:26.169 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:30:26.169 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:37:37.985 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 15:37:37.988 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 15:37:38.096 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 15:37:38.096 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:37:38.149 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:37:38.149 - [任务 19] - Task started 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:37:38.314 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 15:37:38.315 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:37:38.315 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 15:37:38.516 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 15:37:39.103 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 15:37:39.165 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.165 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:37:39.529 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 15:37:39.933 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:37:39.933 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 15:41:17.904 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 15:41:18.722 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:41:20.907 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:41:20.909 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:41:20.909 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:41:21.133 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 18 ms 
[TRACE] 2025-06-19 15:41:26.620 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:41:27.598 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@59a9f549 
[TRACE] 2025-06-19 15:41:27.598 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b7fcc84 
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:41:27.779 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 15:41:27.779 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:41:27.779 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:43:51.285 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 15:43:51.332 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 15:43:51.332 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 15:43:51.393 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:43:51.393 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:43:51.407 - [任务 19] - Task started 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:43:51.797 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 15:43:51.798 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:43:51.798 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 15:43:52.003 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 15:43:52.414 - [任务 19][sqlserver_ad] - building CT table for table dummy_test 
[INFO ] 2025-06-19 15:43:53.797 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"00000028000029D40001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 15:43:53.797 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Starting batch read from table: dummy_test 
[TRACE] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Table dummy_test is going to be initial synced 
[TRACE] 2025-06-19 15:43:54.007 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[INFO ] 2025-06-19 15:43:54.884 - [任务 19][sqlserver_ad] - Table dummy_test has been completed batch read 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: {"currentStartLSN":"00000028000029D40001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:43:55.296 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 15:43:55.308 - [任务 19][local_pg] - Process after table "dummy_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 15:43:55.308 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 15:43:55.598 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:43:55.803 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2025-06-19 15:45:22.971 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 该连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.checkClosed(SQLServerConnection.java:1369)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.checkClosed(SQLServerStatement.java:1092)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.setFetchSize(SQLServerStatement.java:1754)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setFetchSize(HikariProxyPreparedStatement.java)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 15:45:22.971 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 15:45:22, next retry time 2025-06-19 15:46:22 
[INFO ] 2025-06-19 15:46:23.321 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.663000 
[TRACE] 2025-06-19 15:46:23.727 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 15:46:23.727 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 16:13:00.797 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 16:13:00.799 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750319031643 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750319031643 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 16:13:03.691 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3012 ms 
[TRACE] 2025-06-19 16:13:03.691 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 16:13:03.705 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750319031659 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750319031659 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 15 ms 
[TRACE] 2025-06-19 16:13:12.414 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:13:13.388 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@f764eea 
[TRACE] 2025-06-19 16:13:13.388 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@417dbfc3 
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:13:13.509 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 16:13:13.557 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:13:13.557 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:29:06.365 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 16:29:06.366 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 16:29:06.508 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 16:29:06.508 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 16:29:06.561 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 16:29:06.561 - [任务 19] - Task started 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 16:29:06.738 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 16:29:06.738 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 16:29:06.739 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 16:29:06.904 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 16:29:06.905 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000003500005C620004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003500005C620004\"}}" 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: "{\"currentStartLSN\":\"0000003500005C620004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003500005C620004\"}}" 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 16:29:07.073 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 16:29:07.287 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 16:29:07.287 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 16:29:07.898 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 16:31:22.818 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 16:31:23.335 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321746608 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321746608 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 16:31:25.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 16:31:25.690 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321746612 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321746612 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 16:31:25.704 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 16:31:25.909 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 14 ms 
[TRACE] 2025-06-19 16:31:34.842 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:31:35.651 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@32c7260f 
[TRACE] 2025-06-19 16:31:35.653 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4af4a9c5 
[TRACE] 2025-06-19 16:31:35.653 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 16:31:35.770 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:31:35.770 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:31:35.770 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 16:31:35.814 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:31:35.814 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 

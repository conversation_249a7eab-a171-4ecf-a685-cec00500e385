[TRACE] 2025-06-19 14:03:11.167 - [任务 17] - Task initialization... 
[TRACE] 2025-06-19 14:03:11.168 - [任务 17] - Start task milestones: 6853a80551463b1f7959acc7(任务 17) 
[INFO ] 2025-06-19 14:03:11.168 - [任务 17] - Loading table structure completed 
[TRACE] 2025-06-19 14:03:11.297 - [任务 17] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:03:11.401 - [任务 17] - The engine receives 任务 17 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:03:11.401 - [任务 17] - Task started 
[TRACE] 2025-06-19 14:03:11.454 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] start preload schema,table counts: 3 
[TRACE] 2025-06-19 14:03:11.455 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] start preload schema,table counts: 3 
[TRACE] 2025-06-19 14:03:11.455 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:03:11.455 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:03:12.054 - [任务 17][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:03:12.055 - [任务 17][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:03:12.055 - [任务 17][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:03:12.226 - [任务 17][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-19 14:03:12.226 - [任务 17][sqlserver_ad] - The table SourceOfRegion has already exist. 
[ERROR] 2025-06-19 14:04:15.066 - [任务 17][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization error: Failed to init pdk connector, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:205)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	... 7 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 22 more

[TRACE] 2025-06-19 14:04:15.067 - [任务 17][sqlserver -ag1] - Exception skipping - The current exception does not match the skip exception strategy, message: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e 
[ERROR] 2025-06-19 14:04:15.088 - [任务 17][sqlserver -ag1] - com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e <-- Error Message -->
com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	...

<-- Full Stack Trace -->
com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:183)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	... 5 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 22 more

[TRACE] 2025-06-19 14:04:15.089 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] running status set to false 
[TRACE] 2025-06-19 14:04:15.099 - [任务 17][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_a1ba2f1d-4008-40e6-b520-1a74bc820105_1750312991755 
[TRACE] 2025-06-19 14:04:15.100 - [任务 17][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_a1ba2f1d-4008-40e6-b520-1a74bc820105_1750312991755 
[TRACE] 2025-06-19 14:04:15.101 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] schema data cleaned 
[TRACE] 2025-06-19 14:04:15.101 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] monitor closed 
[TRACE] 2025-06-19 14:04:15.103 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] close complete, cost 15 ms 
[TRACE] 2025-06-19 14:04:15.103 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] running status set to false 
[TRACE] 2025-06-19 14:04:15.108 - [任务 17][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_698c9c92-ab4d-40e5-a06c-820dd84543f3_1750312991720 
[TRACE] 2025-06-19 14:04:15.108 - [任务 17][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_698c9c92-ab4d-40e5-a06c-820dd84543f3_1750312991720 
[TRACE] 2025-06-19 14:04:15.108 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] schema data cleaned 
[TRACE] 2025-06-19 14:04:15.109 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] monitor closed 
[TRACE] 2025-06-19 14:04:15.312 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] close complete, cost 6 ms 
[TRACE] 2025-06-19 14:04:21.394 - [任务 17] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:04:22.223 - [任务 17] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@235eb680 
[TRACE] 2025-06-19 14:04:22.224 - [任务 17] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6f10a12e 
[TRACE] 2025-06-19 14:04:22.348 - [任务 17] - Stop task milestones: 6853a80551463b1f7959acc7(任务 17)  
[TRACE] 2025-06-19 14:04:22.348 - [任务 17] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:04:22.349 - [任务 17] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:04:22.350 - [任务 17] - Task stopped. 
[TRACE] 2025-06-19 14:04:22.400 - [任务 17] - Remove memory task client succeed, task: 任务 17[6853a80551463b1f7959acc7] 
[TRACE] 2025-06-19 14:04:22.400 - [任务 17] - Destroy memory task client cache succeed, task: 任务 17[6853a80551463b1f7959acc7] 

[TRACE] 2025-06-19 14:04:30.023 - [任务 18] - Task initialization... 
[TRACE] 2025-06-19 14:04:30.079 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:04:30.079 - [任务 18] - Loading table structure completed 
[TRACE] 2025-06-19 14:04:30.154 - [任务 18] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:04:30.154 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:04:30.171 - [任务 18] - Task started 
[TRACE] 2025-06-19 14:04:30.172 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:04:30.172 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:04:30.172 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:04:30.374 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:04:30.752 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:04:30.752 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:04:30.752 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:04:30.753 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:04:30.815 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:04:30.815 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:04:30.815 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:04:30.945 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-19 14:04:30.945 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:04:30.951 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[TRACE] 2025-06-19 14:04:30.951 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:04:31.138 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:04:31.138 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:04:31.145 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:04:31.145 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:04:31.233 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:04:31.233 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:04:33.972 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:04:33.972 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:04:33.972 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:04:33.973 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:04:33.973 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:04:33.974 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:04:33.974 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[WARN ] 2025-06-19 14:04:34.126 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:04:34.126 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:04:34.327 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:04:34.542 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:04:34.542 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:04:34.747 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:04:37.996 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 2 ms 
[INFO ] 2025-06-19 14:04:37.996 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-19 14:04:55.585 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:04:56.200 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[TRACE] 2025-06-19 14:04:58.568 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313070415 
[TRACE] 2025-06-19 14:04:58.568 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313070415 
[TRACE] 2025-06-19 14:04:58.568 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:04:58.569 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:04:58.570 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3013 ms 
[TRACE] 2025-06-19 14:04:58.570 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:04:58.582 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313070428 
[TRACE] 2025-06-19 14:04:58.582 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313070428 
[TRACE] 2025-06-19 14:04:58.583 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:04:58.583 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:04:58.584 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 13 ms 
[TRACE] 2025-06-19 14:05:07.442 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:05:08.444 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@560317a8 
[TRACE] 2025-06-19 14:05:08.446 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bfb9cac 
[TRACE] 2025-06-19 14:05:08.446 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[TRACE] 2025-06-19 14:05:08.565 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:05:08.566 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:05:08.566 - [任务 18] - Task stopped. 
[TRACE] 2025-06-19 14:05:08.616 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:05:08.619 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:13:15.526 - [任务 18] - Task initialization... 
[TRACE] 2025-06-19 14:13:15.580 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:13:15.581 - [任务 18] - Loading table structure completed 
[TRACE] 2025-06-19 14:13:15.652 - [任务 18] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:13:15.652 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:13:15.668 - [任务 18] - Task started 
[TRACE] 2025-06-19 14:13:15.668 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:15.668 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:13:15.668 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:15.668 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:16.220 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:13:16.220 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:13:16.220 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:13:16.288 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[INFO ] 2025-06-19 14:13:16.288 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:13:16.288 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:13:16.288 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:13:16.288 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-19 14:13:16.417 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[WARN ] 2025-06-19 14:13:16.417 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:13:16.417 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[INFO ] 2025-06-19 14:13:16.544 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:16.594 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:13:16.594 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:13:16.594 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:13:16.594 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:13:16.798 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:13:18.729 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:13:18.729 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:13:18.729 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:13:18.729 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:13:18.730 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:13:18.730 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:18.730 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[WARN ] 2025-06-19 14:13:18.847 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:13:18.850 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:13:18.850 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:13:19.133 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:13:19.134 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:13:19.134 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:13:22.845 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:13:22.845 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-19 14:13:41.857 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:13:42.469 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313595930 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313595930 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3005 ms 
[TRACE] 2025-06-19 14:13:44.806 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:13:44.812 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313595898 
[TRACE] 2025-06-19 14:13:44.812 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313595898 
[TRACE] 2025-06-19 14:13:44.812 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:13:44.812 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:13:44.812 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 5 ms 
[TRACE] 2025-06-19 14:13:50.292 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:13:51.199 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@54db08af 
[TRACE] 2025-06-19 14:13:51.199 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5421282f 
[TRACE] 2025-06-19 14:13:51.316 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[TRACE] 2025-06-19 14:13:51.316 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:13:51.316 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:13:51.316 - [任务 18] - Task stopped. 
[TRACE] 2025-06-19 14:13:51.363 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:13:51.363 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:17:01.463 - [任务 18] - Task initialization... 
[TRACE] 2025-06-19 14:17:01.465 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:17:01.558 - [任务 18] - Loading table structure completed 
[TRACE] 2025-06-19 14:17:01.558 - [任务 18] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:17:01.595 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:17:01.595 - [任务 18] - Task started 
[TRACE] 2025-06-19 14:17:01.605 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:17:01.605 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:17:01.605 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:17:01.605 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:17:02.202 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:17:02.202 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:17:02.202 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:17:02.207 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:17:02.207 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:17:02.208 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:17:02.208 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:17:02.280 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-19 14:17:02.280 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:17:02.280 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[TRACE] 2025-06-19 14:17:02.399 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:17:02.399 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:17:02.447 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:17:02.447 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:17:02.447 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:17:02.447 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:17:02.651 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:17:04.556 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:17:04.556 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:17:04.556 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:17:04.556 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:17:04.557 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:17:04.557 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:17:04.681 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[WARN ] 2025-06-19 14:17:04.681 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:17:04.681 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:17:04.681 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:17:04.955 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:17:04.955 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:17:04.956 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:17:08.345 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:17:08.346 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-19 14:22:13.662 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:22:14.113 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[TRACE] 2025-06-19 14:22:16.514 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313821859 
[TRACE] 2025-06-19 14:22:16.515 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313821859 
[TRACE] 2025-06-19 14:22:16.515 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:22:16.515 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:22:16.516 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3011 ms 
[TRACE] 2025-06-19 14:22:16.516 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:22:16.528 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313821873 
[TRACE] 2025-06-19 14:22:16.528 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313821873 
[TRACE] 2025-06-19 14:22:16.528 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:22:16.528 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:22:16.732 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 12 ms 
[TRACE] 2025-06-19 14:22:23.019 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:22:23.905 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@78e459a6 
[TRACE] 2025-06-19 14:22:23.905 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49562205 
[TRACE] 2025-06-19 14:22:24.024 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[TRACE] 2025-06-19 14:22:24.024 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:22:24.024 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:22:24.025 - [任务 18] - Task stopped. 
[TRACE] 2025-06-19 14:22:24.072 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:22:24.072 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 

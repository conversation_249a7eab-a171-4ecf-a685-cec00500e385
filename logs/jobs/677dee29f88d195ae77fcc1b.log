[TRACE] 2025-01-09 15:47:54.301 - [CDC log cache task from oracle_brige] - Start task milestones: 677dee29f88d195ae77fcc1b(CDC log cache task from oracle_brige) 
[INFO ] 2025-01-09 15:47:54.427 - [CDC log cache task from oracle_brige] - Loading table structure completed 
[TRACE] 2025-01-09 15:47:54.427 - [CDC log cache task from oracle_brige] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-01-09 15:47:54.523 - [CDC log cache task from oracle_brige] - The engine receives CDC log cache task from oracle_brige task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-09 15:47:54.593 - [CDC log cache task from oracle_brige] - Task started 
[TRACE] 2025-01-09 15:47:54.665 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] start preload schema,table counts: 2 
[TRACE] 2025-01-09 15:47:54.666 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] preload schema finished, cost 0 ms 
[TRACE] 2025-01-09 15:47:54.669 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[TRACE] 2025-01-09 15:47:54.707 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-09 15:47:54.709 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=677dee29b6294e7bff483e9c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=677dedd4f88d195ae77fcba7_HANA_CLAIM, version=v2, tableName=HANA_CLAIM, externalStorageTableName=ExternalStorage_SHARE_CDC_1865631204, shareCdcTaskId=677dee29f88d195ae77fcc1b, connectionId=677dedd4f88d195ae77fcba7) 
[INFO ] 2025-01-09 15:47:54.710 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=677dee29b6294e7bff483ea1, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=677dedd4f88d195ae77fcba7_HANA_POLICY, version=v2, tableName=HANA_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1919697814, shareCdcTaskId=677dee29f88d195ae77fcc1b, connectionId=677dedd4f88d195ae77fcba7) 
[INFO ] 2025-01-09 15:47:54.910 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='TapExternalStorage', ttlDay=3] 
[INFO ] 2025-01-09 15:47:57.144 - [CDC log cache task from oracle_brige][oracle_brige] - setting fzs taskId 13a4e44ff3bb4b2da09d92e14cb4a219 
[INFO ] 2025-01-09 15:47:57.232 - [CDC log cache task from oracle_brige][oracle_brige] - fzs set 13a4e44ff3bb4b2da09d92e14cb4a219 taskId ok 
[INFO ] 2025-01-09 15:47:57.376 - [CDC log cache task from oracle_brige][oracle_brige] - set save fzs time ok 
[INFO ] 2025-01-09 15:47:57.395 - [CDC log cache task from oracle_brige][oracle_brige] - set fzs data format ok 
[INFO ] 2025-01-09 15:47:57.395 - [CDC log cache task from oracle_brige][oracle_brige] - fzs clear task ok 
[INFO ] 2025-01-09 15:47:59.404 - [CDC log cache task from oracle_brige][oracle_brige] - Source connector(oracle_brige) initialization completed 
[TRACE] 2025-01-09 15:47:59.404 - [CDC log cache task from oracle_brige][oracle_brige] - Source node "oracle_brige" read batch size: 100 
[TRACE] 2025-01-09 15:47:59.404 - [CDC log cache task from oracle_brige][oracle_brige] - Source node "oracle_brige" event queue capacity: 200 
[INFO ] 2025-01-09 15:47:59.405 - [CDC log cache task from oracle_brige][oracle_brige] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-01-09 15:47:59.412 - [CDC log cache task from oracle_brige][oracle_brige] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":*********,"pendingScn":null,"timestamp":null,"hexScn":"2bc8c5db","fno":0} 
[TRACE] 2025-01-09 15:47:59.413 - [CDC log cache task from oracle_brige][oracle_brige] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-01-09 15:47:59.451 - [CDC log cache task from oracle_brige][oracle_brige] - Batch read completed. 
[TRACE] 2025-01-09 15:47:59.451 - [CDC log cache task from oracle_brige][oracle_brige] - Starting stream read, table list: [HIT_EY_NEWZS.HANA_POLICY, HIT_EY_NEWZS.HANA_CLAIM], offset: {"sortString":null,"offsetValue":null,"lastScn":*********,"pendingScn":null,"timestamp":null,"hexScn":"2bc8c5db","fno":0} 
[INFO ] 2025-01-09 15:47:59.451 - [CDC log cache task from oracle_brige][oracle_brige] - Starting incremental sync using database log parser 
[TRACE] 2025-01-09 15:47:59.617 - [CDC log cache task from oracle_brige][oracle_brige] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在
 
[ERROR] 2025-01-09 15:47:59.628 - [CDC log cache task from oracle_brige][oracle_brige] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在
 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00942: 表或视图不存在

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.lambda$null$3(OracleLogMiner.java:158)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.lambda$multiGetColumnType$4(OracleLogMiner.java:145)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.multiGetColumnType(OracleLogMiner.java:145)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.multiInit(OracleLogMiner.java:117)
	at io.tapdata.connector.oracle.cdc.bridge.BridgeLogMiner.multiInit(BridgeLogMiner.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.multiInit(OracleCdcRunner.java:86)
	at io.tapdata.connector.oracle.OracleConnector.streamReadMultiConnection(OracleConnector.java:472)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$27(HazelcastSourcePdkDataNode.java:863)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.sql.SQLSyntaxErrorException: ORA-00942: 表或视图不存在

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:630)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.connector.oracle.cdc.logminer.OracleLogMiner.lambda$null$3(OracleLogMiner.java:147)
	... 26 more
Caused by: Error : 942, Position : 29, Sql = SELECT * FROM "HIT_EY_NEWZS"."HANA_CLAIM", OriginalSql = SELECT * FROM "HIT_EY_NEWZS"."HANA_CLAIM", Error Msg = ORA-00942: 表或视图不存在

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	... 43 more

[TRACE] 2025-01-09 15:47:59.635 - [CDC log cache task from oracle_brige][oracle_brige] - Job suspend in error handle 
[TRACE] 2025-01-09 15:47:59.670 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] running status set to false 
[TRACE] 2025-01-09 15:47:59.677 - [CDC log cache task from oracle_brige][oracle_brige] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_1492e70792144a8b86e3860f80aed780_1736408874780 
[TRACE] 2025-01-09 15:47:59.679 - [CDC log cache task from oracle_brige][oracle_brige] - PDK connector node released: HazelcastSourcePdkShareCDCNode_1492e70792144a8b86e3860f80aed780_1736408874780 
[TRACE] 2025-01-09 15:47:59.679 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] schema data cleaned 
[TRACE] 2025-01-09 15:47:59.679 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] monitor closed 
[TRACE] 2025-01-09 15:47:59.683 - [CDC log cache task from oracle_brige][oracle_brige] - Node oracle_brige[1492e70792144a8b86e3860f80aed780] close complete, cost 36 ms 
[TRACE] 2025-01-09 15:47:59.683 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ab132c1ac2c42f2a10a89a442c01194] running status set to false 
[TRACE] 2025-01-09 15:47:59.684 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[TRACE] 2025-01-09 15:47:59.684 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - PDK connector node released: null 
[TRACE] 2025-01-09 15:47:59.684 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ab132c1ac2c42f2a10a89a442c01194] schema data cleaned 
[TRACE] 2025-01-09 15:47:59.684 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ab132c1ac2c42f2a10a89a442c01194] monitor closed 
[TRACE] 2025-01-09 15:47:59.688 - [CDC log cache task from oracle_brige][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2ab132c1ac2c42f2a10a89a442c01194] close complete, cost 2 ms 
[INFO ] 2025-01-09 15:48:01.321 - [CDC log cache task from oracle_brige] - Task [CDC log cache task from oracle_brige] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-01-09 15:48:01.328 - [CDC log cache task from oracle_brige] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-09 15:48:01.328 - [CDC log cache task from oracle_brige] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@445b71fe 
[TRACE] 2025-01-09 15:48:01.447 - [CDC log cache task from oracle_brige] - Stop task milestones: 677dee29f88d195ae77fcc1b(CDC log cache task from oracle_brige)  
[TRACE] 2025-01-09 15:48:01.447 - [CDC log cache task from oracle_brige] - Stopped task aspect(s) 
[TRACE] 2025-01-09 15:48:01.448 - [CDC log cache task from oracle_brige] - Snapshot order controller have been removed 
[INFO ] 2025-01-09 15:48:01.448 - [CDC log cache task from oracle_brige] - Task stopped. 
[TRACE] 2025-01-09 15:48:01.469 - [CDC log cache task from oracle_brige] - Remove memory task client succeed, task: CDC log cache task from oracle_brige[677dee29f88d195ae77fcc1b] 
[TRACE] 2025-01-09 15:48:01.472 - [CDC log cache task from oracle_brige] - Destroy memory task client cache succeed, task: CDC log cache task from oracle_brige[677dee29f88d195ae77fcc1b] 

[WARN ] 2025-06-19 00:11:34.683  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 912842 ms
[INFO ] 2025-06-19 00:11:35.079  [WebSocketClient-AsyncIO-397] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 00:11:35.080  [WebSocketClient-AsyncIO-397] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 00:11:37.643  [hz.zed_flow_engine.cached.thread-17] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-18 23:56:19.798 to 2025-06-19 00:11:37.643 since last heartbeat (+912845 ms)
[WARN ] 2025-06-19 00:11:37.643  [hz.zed_flow_engine.cached.thread-17] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 912845 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 00:11:37.687  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 2994616 ms
[INFO ] 2025-06-19 00:11:38.838  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 00:11:38.839  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 00:29:33.641  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[WARN ] 2025-06-19 00:29:34.243  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1073560 ms
[INFO ] 2025-06-19 00:29:36.205  [hz.zed_flow_engine.cached.thread-17] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 00:11:37.643 to 2025-06-19 00:29:36.204 since last heartbeat (+1073561 ms)
[WARN ] 2025-06-19 00:29:36.205  [hz.zed_flow_engine.cached.thread-17] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1073561 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 00:46:05.839  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 986591 ms
[INFO ] 2025-06-19 00:46:06.844  [nioEventLoopGroup-9-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 00:46:06.844  [nioEventLoopGroup-9-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 00:46:06.844  [nioEventLoopGroup-9-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 00:46:07.158  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 00:46:07.170  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjY1MTk3LCJpYXQiOjE3NTAyNjUxNjd9.WpvbHc7n7OD42WhcSZbQzAjqszSS66dqIp7aHjhq4wk]
[INFO ] 2025-06-19 00:46:07.172  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/c90d04c5f561e68430e8298ace5a337e wsPort 8246]
[INFO ] 2025-06-19 00:46:07.178  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 00:46:07.178  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjY1MTk3LCJpYXQiOjE3NTAyNjUxNjd9.WpvbHc7n7OD42WhcSZbQzAjqszSS66dqIp7aHjhq4wk]
[INFO ] 2025-06-19 00:46:07.179  [nioEventLoopGroup-11-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 00:46:07.180  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 00:46:07.803  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 00:29:36.204 to 2025-06-19 00:46:07.802 since last heartbeat (+986598 ms)
[WARN ] 2025-06-19 00:46:07.804  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 986598 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 00:46:09.118  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 00:46:10.257  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 00:46:10.258  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-19 01:03:22.654  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1031805 ms
[INFO ] 2025-06-19 01:03:24.378  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 00:46:07.802 to 2025-06-19 01:03:24.377 since last heartbeat (+1031575 ms)
[WARN ] 2025-06-19 01:03:24.379  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1031575 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 01:03:26.829  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[WARN ] 2025-06-19 01:13:08.962  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 580545 ms
[INFO ] 2025-06-19 01:13:09.816  [hz.zed_flow_engine.cached.thread-1] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 01:03:24.377 to 2025-06-19 01:13:09.815 since last heartbeat (+580438 ms)
[WARN ] 2025-06-19 01:13:09.816  [hz.zed_flow_engine.cached.thread-1] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 580438 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 01:13:11.239  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 01:13:11.239  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 01:13:11.248  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 01:13:17.270  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 01:13:17.270  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 01:31:08.277  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 01:13:14.818 to 2025-06-19 01:31:08.277 since last heartbeat (+1068459 ms)
[WARN ] 2025-06-19 01:31:08.277  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1068459 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 01:31:08.320  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1068460 ms
[INFO ] 2025-06-19 01:31:09.829  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 01:31:09.829  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 01:31:10.730  [WebSocketClient-AsyncIO-1] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 01:48:43.987  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 01:31:08.277 to 2025-06-19 01:48:43.987 since last heartbeat (+1050710 ms)
[WARN ] 2025-06-19 01:48:43.988  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1050710 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 01:48:44.031  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1050717 ms
[INFO ] 2025-06-19 02:04:47.672  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 01:48:43.987 to 2025-06-19 02:04:47.671 since last heartbeat (+958684 ms)
[WARN ] 2025-06-19 02:04:47.761  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 958684 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 02:04:47.714  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 958681 ms
[INFO ] 2025-06-19 02:04:49.354  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 02:04:50.131  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 02:04:50.131  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 02:21:25.652  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 02:21:25.652  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[WARN ] 2025-06-19 02:21:26.451  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 992741 ms
[INFO ] 2025-06-19 02:21:27.870  [WebSocketClient-AsyncIO-7] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 02:21:30.409  [hz.zed_flow_engine.cached.thread-19] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 02:04:52.672 to 2025-06-19 02:21:30.409 since last heartbeat (+992737 ms)
[WARN ] 2025-06-19 02:21:30.409  [hz.zed_flow_engine.cached.thread-19] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 992737 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 02:39:01.157  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1049705 ms
[INFO ] 2025-06-19 02:39:01.919  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 02:39:01.920  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 02:39:02.066  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 02:39:05.120  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 02:21:30.409 to 2025-06-19 02:39:05.119 since last heartbeat (+1049710 ms)
[WARN ] 2025-06-19 02:39:05.120  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1049710 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 02:55:01.811  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 955651 ms
[INFO ] 2025-06-19 02:55:03.243  [WebSocketClient-AsyncIO-7] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 02:55:03.243  [WebSocketClient-AsyncIO-7] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 02:55:05.773  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 02:39:05.119 to 2025-06-19 02:55:05.773 since last heartbeat (+955654 ms)
[WARN ] 2025-06-19 02:55:05.773  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 955654 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 03:12:20.643  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1033835 ms
[INFO ] 2025-06-19 03:12:21.555  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 03:12:21.556  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 03:12:21.963  [WebSocketClient-AsyncIO-4] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 03:12:24.497  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 02:55:05.773 to 2025-06-19 03:12:24.496 since last heartbeat (+1033723 ms)
[WARN ] 2025-06-19 03:12:24.498  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1033723 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 03:30:11.265  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1064730 ms
[INFO ] 2025-06-19 03:30:13.255  [nioEventLoopGroup-11-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 03:30:13.255  [nioEventLoopGroup-11-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 03:30:13.256  [nioEventLoopGroup-11-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 03:30:13.565  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 03:30:13.578  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjc1MDQzLCJpYXQiOjE3NTAyNzUwMTN9.PFmsvoRs76WSfDx3G91KOLRwNMW1p1WEcjuSucjyn6Y]
[INFO ] 2025-06-19 03:30:13.581  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/c90d04c5f561e68430e8298ace5a337e wsPort 8246]
[INFO ] 2025-06-19 03:30:13.587  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 03:30:13.587  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjc1MDQzLCJpYXQiOjE3NTAyNzUwMTN9.PFmsvoRs76WSfDx3G91KOLRwNMW1p1WEcjuSucjyn6Y]
[INFO ] 2025-06-19 03:30:13.588  [nioEventLoopGroup-13-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 03:30:13.588  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 03:30:14.222  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 03:12:24.496 to 2025-06-19 03:30:14.222 since last heartbeat (+1064726 ms)
[WARN ] 2025-06-19 03:30:14.223  [hz.zed_flow_engine.cached.thread-9] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1064726 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 03:45:38.983  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 922725 ms
[INFO ] 2025-06-19 03:45:39.135  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 03:45:39.411  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 03:45:39.411  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 03:45:41.944  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 03:30:14.222 to 2025-06-19 03:45:41.943 since last heartbeat (+922721 ms)
[WARN ] 2025-06-19 03:45:41.945  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 922721 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 03:45:41.979  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 12769292 ms
[WARN ] 2025-06-19 04:02:44.707  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1020720 ms
[INFO ] 2025-06-19 04:02:45.143  [WebSocketClient-AsyncIO-9] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 04:02:47.670  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 03:45:41.943 to 2025-06-19 04:02:47.670 since last heartbeat (+1020727 ms)
[WARN ] 2025-06-19 04:02:47.671  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1020727 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 04:19:56.630  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 04:19:56.630  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 04:19:56.634  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 04:19:57.353  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1026643 ms
[INFO ] 2025-06-19 04:19:59.311  [hz.zed_flow_engine.cached.thread-16] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 04:02:47.670 to 2025-06-19 04:19:59.310 since last heartbeat (+1026640 ms)
[WARN ] 2025-06-19 04:19:59.311  [hz.zed_flow_engine.cached.thread-16] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1026640 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 04:37:41.627  [WebSocketClient-AsyncIO-9] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 04:37:41.627  [WebSocketClient-AsyncIO-9] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-19 04:37:42.050  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1059701 ms
[INFO ] 2025-06-19 04:37:44.015  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 04:19:59.310 to 2025-06-19 04:37:44.012 since last heartbeat (+1059702 ms)
[WARN ] 2025-06-19 04:37:44.015  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1059702 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 04:37:46.494  [WebSocketClient-AsyncIO-3] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 04:37:46.555  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 04:37:46.556  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 04:37:46.571  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 04:55:41.774  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1074722 ms
[INFO ] 2025-06-19 04:55:43.735  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 04:37:44.012 to 2025-06-19 04:55:43.734 since last heartbeat (+1074722 ms)
[WARN ] 2025-06-19 04:55:43.735  [hz.zed_flow_engine.cached.thread-20] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1074722 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 05:12:27.620  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1000846 ms
[INFO ] 2025-06-19 05:12:28.427  [nioEventLoopGroup-13-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 05:12:28.427  [nioEventLoopGroup-13-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 05:12:28.427  [nioEventLoopGroup-13-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 05:12:28.737  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 05:12:28.747  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjgxMTc4LCJpYXQiOjE3NTAyODExNDh9.uTnLqF-Qi2JAdHq5h0-xRh8TmRcAHdm6fcnIoJKfqVc]
[INFO ] 2025-06-19 05:12:28.749  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/c90d04c5f561e68430e8298ace5a337e wsPort 8246]
[INFO ] 2025-06-19 05:12:28.754  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 05:12:28.754  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjgxMTc4LCJpYXQiOjE3NTAyODExNDh9.uTnLqF-Qi2JAdHq5h0-xRh8TmRcAHdm6fcnIoJKfqVc]
[INFO ] 2025-06-19 05:12:28.756  [nioEventLoopGroup-15-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 05:12:28.756  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 05:12:29.385  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 04:55:43.734 to 2025-06-19 05:12:29.384 since last heartbeat (+1000650 ms)
[WARN ] 2025-06-19 05:12:29.385  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1000650 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 05:12:31.874  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 05:12:31.874  [WebSocketClient-AsyncIO-3] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 05:12:32.056  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 05:12:32.056  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 05:29:15.159  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1001730 ms
[INFO ] 2025-06-19 05:29:16.115  [hz.zed_flow_engine.cached.thread-19] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 05:12:29.384 to 2025-06-19 05:29:16.115 since last heartbeat (+1001731 ms)
[WARN ] 2025-06-19 05:29:16.116  [hz.zed_flow_engine.cached.thread-19] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1001731 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 05:29:16.361  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 05:29:16.361  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 05:29:18.606  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[WARN ] 2025-06-19 05:46:32.802  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1032643 ms
[INFO ] 2025-06-19 05:46:33.766  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 05:29:16.115 to 2025-06-19 05:46:33.765 since last heartbeat (+1032650 ms)
[WARN ] 2025-06-19 05:46:33.767  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1032650 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 05:46:36.556  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 06:04:05.610  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1047802 ms
[INFO ] 2025-06-19 06:04:06.489  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 05:46:33.765 to 2025-06-19 06:04:06.488 since last heartbeat (+1047723 ms)
[WARN ] 2025-06-19 06:04:06.489  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1047723 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 06:04:08.989  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 06:04:08.990  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 06:21:18.792  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 06:04:06.488 to 2025-06-19 06:21:18.791 since last heartbeat (+1027303 ms)
[WARN ] 2025-06-19 06:21:18.792  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1027303 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 06:21:18.828  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1027296 ms
[INFO ] 2025-06-19 06:21:21.008  [WebSocketClient-AsyncIO-12] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 06:21:21.415  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 06:21:21.416  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 06:21:21.424  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 06:21:31.017  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 06:21:31.017  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 06:21:31.540  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 06:21:31.541  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 06:21:48.549  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 9291570 ms
[INFO ] 2025-06-19 06:22:03.763  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 06:22:03.763  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[WARN ] 2025-06-19 06:38:16.333  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 949783 ms
[INFO ] 2025-06-19 06:38:17.330  [nioEventLoopGroup-15-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 06:38:17.330  [nioEventLoopGroup-15-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 06:38:17.330  [nioEventLoopGroup-15-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 06:38:17.640  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 06:38:17.669  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjg2MzI3LCJpYXQiOjE3NTAyODYyOTd9.hpsJSvI-wQZAf3ZvtHf7RKOGy3_cu92F68oLxcvTdgk]
[INFO ] 2025-06-19 06:38:17.672  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/c90d04c5f561e68430e8298ace5a337e wsPort 8246]
[INFO ] 2025-06-19 06:38:17.680  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 06:38:17.680  [ForkJoinPool.commonPool-worker-15] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjg2MzI3LCJpYXQiOjE3NTAyODYyOTd9.hpsJSvI-wQZAf3ZvtHf7RKOGy3_cu92F68oLxcvTdgk]
[INFO ] 2025-06-19 06:38:17.683  [nioEventLoopGroup-17-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 06:38:17.683  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 06:38:18.290  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 06:22:23.506 to 2025-06-19 06:38:18.290 since last heartbeat (+949784 ms)
[WARN ] 2025-06-19 06:38:18.291  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 949784 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 06:55:58.007  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1055535 ms
[INFO ] 2025-06-19 06:55:58.969  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 06:38:18.290 to 2025-06-19 06:55:58.969 since last heartbeat (+1055679 ms)
[WARN ] 2025-06-19 06:55:58.970  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1055679 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 06:56:01.495  [WebSocketClient-AsyncIO-400] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[WARN ] 2025-06-19 07:11:31.604  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 928595 ms
[INFO ] 2025-06-19 07:11:32.565  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 06:55:58.969 to 2025-06-19 07:11:32.564 since last heartbeat (+928595 ms)
[WARN ] 2025-06-19 07:11:32.565  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 928595 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 07:11:36.404  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 07:27:59.423  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 982816 ms
[INFO ] 2025-06-19 07:28:00.155  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 07:11:32.564 to 2025-06-19 07:28:00.154 since last heartbeat (+982590 ms)
[WARN ] 2025-06-19 07:28:00.155  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 982590 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 07:28:02.759  [WebSocketClient-AsyncIO-400] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 07:28:02.760  [WebSocketClient-AsyncIO-400] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 07:45:53.814  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 07:28:00.154 to 2025-06-19 07:45:53.814 since last heartbeat (+1068660 ms)
[WARN ] 2025-06-19 07:45:53.815  [hz.zed_flow_engine.cached.thread-21] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1068660 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 07:45:53.850  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1068581 ms
[INFO ] 2025-06-19 07:45:56.347  [WebSocketClient-AsyncIO-5] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 07:45:57.777  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 07:45:57.777  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 07:45:57.783  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 08:02:54.503  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1015642 ms
[INFO ] 2025-06-19 08:02:54.505  [hz.zed_flow_engine.cached.thread-14] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 07:45:53.814 to 2025-06-19 08:02:54.505 since last heartbeat (+1015691 ms)
[WARN ] 2025-06-19 08:02:54.611  [hz.zed_flow_engine.cached.thread-14] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1015691 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 08:09:11.973  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 371559 ms
[INFO ] 2025-06-19 08:09:13.469  [WebSocketClient-AsyncIO-5] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:09:13.470  [WebSocketClient-AsyncIO-5] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:09:15.015  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:09:15.015  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:09:15.933  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 08:02:59.370 to 2025-06-19 08:09:15.933 since last heartbeat (+371563 ms)
[WARN ] 2025-06-19 08:09:15.934  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 371563 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 08:09:15.975  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 6372426 ms
[INFO ] 2025-06-19 08:09:16.196  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 08:09:16.196  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 08:09:18.466  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 08:26:59.642  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 08:09:15.933 to 2025-06-19 08:26:59.642 since last heartbeat (+1058709 ms)
[WARN ] 2025-06-19 08:26:59.642  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 1058709 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 08:26:59.681  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 1058715 ms
[INFO ] 2025-06-19 08:27:03.914  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 08:30:30.010  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 205258 ms
[INFO ] 2025-06-19 08:30:32.249  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:30:32.250  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:30:34.702  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 08:27:04.712 to 2025-06-19 08:30:34.701 since last heartbeat (+204989 ms)
[WARN ] 2025-06-19 08:30:34.703  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 204989 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 08:30:37.250  [WebSocketClient-AsyncIO-1] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 08:30:39.016  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:30:39.016  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:30:39.025  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 08:32:08.128  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 83427 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 08:32:08.170  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 83426 ms
[INFO ] 2025-06-19 08:32:10.685  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:32:10.686  [WebSocketClient-AsyncIO-1] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:32:12.564  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:32:12.564  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:32:15.680  [WebSocketClient-AsyncIO-8] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[WARN ] 2025-06-19 08:33:32.471  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 72304 ms
[INFO ] 2025-06-19 08:33:34.999  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[WARN ] 2025-06-19 08:33:35.427  [hz.zed_flow_engine.cached.thread-18] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 72299 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 08:33:37.988  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:33:37.989  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-19 08:48:39.567  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 898103 ms
[INFO ] 2025-06-19 08:48:40.974  [WebSocketClient-AsyncIO-6] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 08:48:43.098  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 08:48:43.098  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 08:48:43.104  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 08:48:43.410  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 08:33:40.428 to 2025-06-19 08:48:43.409 since last heartbeat (+897981 ms)
[WARN ] 2025-06-19 08:48:43.410  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 897981 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 08:59:56.177  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 670725 ms
[INFO ] 2025-06-19 08:59:58.182  [nioEventLoopGroup-17-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 08:59:58.182  [nioEventLoopGroup-17-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 08:59:58.182  [nioEventLoopGroup-17-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 08:59:58.487  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 08:59:58.499  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjk0ODI4LCJpYXQiOjE3NTAyOTQ3OTh9.lWmYjaRLA3WYyuMklRRlPK5QURx-77YL7dUpAUspoO8]
[INFO ] 2025-06-19 08:59:58.502  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/c90d04c5f561e68430e8298ace5a337e wsPort 8246]
[INFO ] 2025-06-19 08:59:58.508  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 08:59:58.508  [ForkJoinPool.commonPool-worker-16] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiI2MjgxODg2NjAiLCJzZXJ2aWNlIjoiZW5naW5lIiwiY2xpZW50SWQiOiJ6ZWRfZmxvd19lbmdpbmVfMDNjMDdmNDEwZWE2NDZkODlkZDAwY2M5ZDc5ODEwMGIiLCJ0ZXJtaW5hbCI6MSwidWlkIjoiNjJiYzUwMDhkNDk1OGQwMTNkOTdjN2E2IiwiZXhwIjoxNzUwMjk0ODI4LCJpYXQiOjE3NTAyOTQ3OTh9.lWmYjaRLA3WYyuMklRRlPK5QURx-77YL7dUpAUspoO8]
[INFO ] 2025-06-19 08:59:58.509  [nioEventLoopGroup-19-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 08:59:58.509  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 08:59:59.142  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 08:48:43.409 to 2025-06-19 08:59:59.141 since last heartbeat (+670732 ms)
[WARN ] 2025-06-19 08:59:59.142  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 670732 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 09:00:01.708  [WebSocketClient-AsyncIO-6] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:00:01.708  [WebSocketClient-AsyncIO-6] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 09:00:03.944  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:00:03.944  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 09:00:06.711  [WebSocketClient-AsyncIO-11] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 09:00:17.216  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 09:00:19.860  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:00:19.860  [WebSocketClient-AsyncIO-11] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[WARN ] 2025-06-19 09:00:22.322  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 2991347 ms
[INFO ] 2025-06-19 09:00:27.325  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:00:27.326  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 09:01:37.247  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 68922 ms
[WARN ] 2025-06-19 09:01:41.083  [hz.zed_flow_engine.cached.thread-3] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 68769 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 09:01:42.665  [hz.zed_flow_engine.HealthMonitor] HealthMonitor - [**********]:5701 [dev] [5.5.0] processors=12, physical.memory.total=32.0G, physical.memory.free=94.0M, swap.space.total=16.0G, swap.space.free=1.4G, heap.memory.used=129.7M, heap.memory.free=130.3M, heap.memory.total=260.0M, heap.memory.max=8.0G, heap.memory.used/total=49.90%, heap.memory.used/max=1.58%, minor.gc.count=105, minor.gc.time=954ms, major.gc.count=0, major.gc.time=0ms, load.process=6.65%, load.system=100.00%, load.systemAverage=8.56, thread.count=235, thread.peakCount=266, cluster.timeDiff=-46050047, event.q.size=0, executor.q.async.size=0, executor.q.client.size=0, executor.q.client.query.size=0, executor.q.client.blocking.size=0, executor.q.query.size=0, executor.q.scheduled.size=0, executor.q.io.size=0, executor.q.system.size=0, executor.q.operations.size=0, executor.q.priorityOperation.size=0, operations.completed.count=1, executor.q.mapLoad.size=0, executor.q.mapLoadAllKeys.size=0, executor.q.cluster.size=0, executor.q.response.size=0, operations.running.count=0, operations.pending.invocations.percentage=0.00%, operations.pending.invocations.count=0, proxy.count=0, clientEndpoint.count=0, connection.active.count=0, client.connection.count=0, connection.count=0
[INFO ] 2025-06-19 09:01:48.617  [WebSocketClient-AsyncIO-4] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06, status code: 1000, reason: null
[INFO ] 2025-06-19 09:01:56.303  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cd607dc13c0d4132bfdcd240f36c8e6ed5da2505c95546e4a82d96a94eeaa904&singletonTag=bbcce66b-3e34-4a64-95d1-be19499bec06
[INFO ] 2025-06-19 09:01:58.623  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:01:58.624  [WebSocketClient-AsyncIO-4] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 09:02:06.413  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 09:02:06.414  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 09:02:46.074  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 68752 ms
[INFO ] 2025-06-19 09:05:16.321  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:05:16.321  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:10:16.337  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:10:16.337  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:15:16.358  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:15:16.358  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:20:16.380  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:20:16.380  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:25:16.402  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:25:16.402  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:30:16.425  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:30:16.425  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:36:14.459  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:36:14.459  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:41:14.481  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:41:14.481  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:46:14.499  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:46:14.499  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:51:14.520  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 09:51:14.520  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:56:14.545  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 09:56:14.545  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:01:14.562  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:01:14.562  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:06:14.588  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:06:14.588  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:11:14.609  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:11:14.609  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:16:14.625  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:16:14.625  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:21:16.725  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:21:16.725  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:26:16.748  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:26:16.748  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:31:16.774  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:31:16.774  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:37:16.731  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:37:16.731  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:43:16.739  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:43:16.739  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:44:48.138  [Thread-websocket-handle-message--2-thread-24] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"null","connectionName":"sql_tt","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"downLoadConnector"}
[INFO ] 2025-06-19 10:44:48.185  [DOWNLOAD-CONNECTOR-sql_tt] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 10:44:48.248  [Thread-websocket-handle-message--2-thread-1] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"null","connectionName":"sql_tt","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"testConnection"}
[INFO ] 2025-06-19 10:44:48.249  [Thread-514] TestConnectionHandler - Starting validate connections name: sql_tt.
[INFO ] 2025-06-19 10:48:16.752  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:48:16.752  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:53:16.765  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 10:53:16.765  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:58:16.841  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 10:58:16.841  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 11:03:16.852  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 11:03:16.852  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 11:08:16.871  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 11:08:16.871  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 11:13:16.889  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 11:13:16.889  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 11:18:16.903  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 11:18:16.903  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 11:23:16.916  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 11:23:16.916  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:23:03.972  [main] Application - disabledAlgorithms [SSLv3, TLSv1, TLSv1.1, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]->[SSLv3, DTLSv1.0, RC4, DES, MD5withRSA, DH keySize < 1024, EC keySize < 224, 3DES_EDE_CBC, anon, NULL, ECDH]
[INFO ] 2025-06-19 12:23:04.008  [main] Application - Starting application, code version 2025-06-17T06:38:53Z
[INFO ] 2025-06-19 12:23:04.226  [background-preinit] Version - HV000001: Hibernate Validator 9.0.0.CR1
[INFO ] 2025-06-19 12:23:04.255  [main] Application - Starting Application using Java 17.0.14 with PID 7349 (/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/iengine-app/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
[INFO ] 2025-06-19 12:23:04.256  [main] Application - No active profile set, falling back to 1 default profile: "default"
[INFO ] 2025-06-19 12:23:04.804  [main] RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[INFO ] 2025-06-19 12:23:04.895  [main] RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 83 ms. Found 0 MongoDB repository interfaces.
[INFO ] 2025-06-19 12:23:05.016  [main] ConnectorManager - TAPDATA_MONGO_CONN env variable does not set, will use default 
[INFO ] 2025-06-19 12:23:05.016  [main] ConnectorManager - ssl env variable does not set, will use default false
[INFO ] 2025-06-19 12:23:05.016  [main] ConnectorManager - cloud_accessCode env variable does not set, will use default "".
[INFO ] 2025-06-19 12:23:05.016  [main] ConnectorManager - cloud_retryTime env variable does not set, will use default 3
[INFO ] 2025-06-19 12:23:05.018  [main] CloudSignUtil - ak/sk needSign false, accessKey null, secretKey null
[INFO ] 2025-06-19 12:23:05.018  [main] ConnectorManager - mode env variable does not set, will use default cluster
[INFO ] 2025-06-19 12:23:05.035  [main] ConnectorManager - 
Initialed variable
 - mongoURI: mongodb://localhost:27017/tapdata_355
 - ssl: false
 - sslCA: 
 - sslPEM: 
 - mongodbConnParams: 
 - baseURLs: [http://localhost:3000/api/]
 - accessCode: 
 - restRetryTime: 3
 - mode: cluster
 - app_type: DAAS
 - process id: zed_flow_engine
 - job tags: null
 - region: null
 - zone: null
 - worker dir: null
[WARN ] 2025-06-19 12:23:05.258  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[WARN ] 2025-06-19 12:23:05.277  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[INFO ] 2025-06-19 12:23:05.365  [main] ConnectorManager - Available processors number: 12
[INFO ] 2025-06-19 12:23:05.367  [main] ConnectorManager - Java class path: /Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/iengine-app/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-enterprise/iengine-enterprise/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/api/target/classes:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.7/spring-test-6.2.7.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-sync/5.3.0/mongodb-driver-sync-5.3.0.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/5.4.0/bson-5.4.0.jar:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/5.4.0/mongodb-driver-core-5.4.0.jar:/Users/<USER>/.m2/repository/org/mongodb/bson-record-codec/5.4.0/bson-record-codec-5.4.0.jar:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/tapdata-storage-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-pdk-runner/target/classes:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.12.6/jjwt-api-0.12.6.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-impl/0.12.6/jjwt-impl-0.12.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-jackson/0.12.6/jjwt-jackson-0.12.6.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar:/Users/<USER>/.m2/repository/cglib/cglib/3.3.0/cglib-3.3.0.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.2/commons-codec-1.17.2.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.2-5/zstd-jni-1.5.2-5.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/org/rocksdb/rocksdbjni/7.3.1/rocksdbjni-7.3.1.jar:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/async-tools-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/proxy-client-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/websocket-client-module/target/classes:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.119.Final/netty-all-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.119.Final/netty-buffer-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.119.Final/netty-codec-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.119.Final/netty-codec-dns-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-haproxy/4.1.119.Final/netty-codec-haproxy-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.119.Final/netty-codec-http-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.119.Final/netty-codec-http2-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-memcache/4.1.119.Final/netty-codec-memcache-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-mqtt/4.1.119.Final/netty-codec-mqtt-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-redis/4.1.119.Final/netty-codec-redis-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-smtp/4.1.119.Final/netty-codec-smtp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.119.Final/netty-codec-socks-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-stomp/4.1.119.Final/netty-codec-stomp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-xml/4.1.119.Final/netty-codec-xml-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.119.Final/netty-common-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.119.Final/netty-handler-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.119.Final/netty-transport-native-unix-common-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.119.Final/netty-handler-proxy-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-ssl-ocsp/4.1.119.Final/netty-handler-ssl-ocsp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.119.Final/netty-resolver-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.119.Final/netty-resolver-dns-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.119.Final/netty-transport-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-rxtx/4.1.119.Final/netty-transport-rxtx-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-sctp/4.1.119.Final/netty-transport-sctp-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-udt/4.1.119.Final/netty-transport-udt-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.119.Final/netty-transport-classes-epoll-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-kqueue/4.1.119.Final/netty-transport-classes-kqueue-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.119.Final/netty-resolver-dns-classes-macos-4.1.119.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-aarch_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.119.Final/netty-transport-native-epoll-4.1.119.Final-linux-riscv64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-kqueue/4.1.119.Final/netty-transport-native-kqueue-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.119.Final/netty-resolver-dns-native-macos-4.1.119.Final-osx-aarch_64.jar:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/modules-api/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-api/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-pdk-api/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/observable-module/target/classes:/Users/<USER>/.m2/repository/net/openhft/chronicle-queue/5.21.91/chronicle-queue-5.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-core/2.21.91/chronicle-core-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-analytics/2.21ea0/chronicle-analytics-2.21ea0.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-bytes/2.21.89/chronicle-bytes-2.21.89.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-wire/2.21.91/chronicle-wire-2.21.91.jar:/Users/<USER>/.m2/repository/net/openhft/compiler/2.21ea80/compiler-2.21ea80.jar:/Users/<USER>/.m2/repository/net/openhft/chronicle-threads/2.21.86/chronicle-threads-2.21.86.jar:/Users/<USER>/.m2/repository/net/openhft/affinity/3.21ea5/affinity-3.21ea5.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.4/commons-cli-1.4.jar:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/milestone-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/skip-error-event-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/test-run-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/deduction-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/custom-sql-filter-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/service-skeleton-module/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-modules/script-engines-module/target/classes:/Users/<USER>/.m2/repository/org/graalvm/js/js-language/24.1.2/js-language-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/regex/regex/24.1.2/regex-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-api/24.1.2/truffle-api-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/shadowed/icu4j/24.1.2/icu4j-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-runtime/24.1.2/truffle-runtime-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-enterprise/24.1.2/truffle-enterprise-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/truffle/truffle-compiler/24.1.2/truffle-compiler-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/jniutils/24.1.2/jniutils-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/nativebridge/24.1.2/nativebridge-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/js/js-scriptengine/24.1.2/js-scriptengine-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/polyglot/polyglot/24.1.2/polyglot-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/collections/24.1.2/collections-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/nativeimage/24.1.2/nativeimage-24.1.2.jar:/Users/<USER>/.m2/repository/org/graalvm/sdk/word/24.1.2/word-24.1.2.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/33.4.0-jre/guava-33.4.0-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.43.0/checker-qual-3.43.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.36.0/error_prone_annotations-2.36.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.18.0/commons-io-2.18.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.0/spring-boot-starter-web-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.0/spring-boot-starter-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.0/spring-boot-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.0/spring-boot-autoconfigure-3.5.0.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.0/spring-boot-starter-json-3.5.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.0/jackson-datatype-jdk8-2.19.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.0/jackson-module-parameter-names-2.19.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.0/spring-boot-starter-tomcat-3.5.0.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.41/tomcat-embed-core-10.1.41.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.41/tomcat-embed-el-10.1.41.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.41/tomcat-embed-websocket-10.1.41.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.2/spring-web-6.2.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.0/micrometer-observation-1.15.0.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.0/micrometer-commons-1.15.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.7/spring-webmvc-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.7/spring-aop-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.7/spring-expression-6.2.7.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/9.0.0.CR1/hibernate-validator-9.0.0.CR1.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.0/jackson-dataformat-yaml-2.19.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.3/jackson-databind-2.18.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.3/jackson-annotations-2.18.3.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.4/snakeyaml-2.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.3/jackson-core-2.18.3.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.7/spring-websocket-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.7/spring-context-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.7/spring-core-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.7/spring-jcl-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.7/spring-messaging-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.7/spring-beans-6.2.7.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.5.0-M3/commons-collections4-4.5.0-M3.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.0/spring-boot-starter-jdbc-3.5.0.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.7/spring-jdbc-6.2.7.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.7/spring-tx-6.2.7.jar:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.3/jsqlparser-4.3.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-1.2-api/2.17.1/log4j-1.2-api-2.17.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-mongodb/3.5.0/spring-boot-starter-data-mongodb-3.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-mongodb/4.5.0/spring-data-mongodb-4.5.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.0/spring-data-commons-3.5.0.jar:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/iengine-common/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.3/jackson-datatype-jsr310-2.18.3.jar:/Users/<USER>/.m2/repository/org/json/json/20250107/json-20250107.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.12.1/gson-2.12.1.jar:/Users/<USER>/.m2/repository/commons-net/commons-net/3.11.1/commons-net-3.11.1.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.11.0/commons-beanutils-1.11.0.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar:/Users/<USER>/.m2/repository/org/dom4j/dom4j/2.1.4/dom4j-2.1.4.jar:/Users/<USER>/.m2/repository/com/github/albfernandez/juniversalchardet/2.3.0/juniversalchardet-2.3.0.jar:/Users/<USER>/.m2/repository/org/apache/avro/avro/1.12.0/avro-1.12.0.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka_2.13/3.9.1/kafka_2.13-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-server-common/3.9.1/kafka-server-common-3.9.1.jar:/Users/<USER>/.m2/repository/org/pcollections/pcollections/4.0.1/pcollections-4.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-group-coordinator-api/3.9.1/kafka-group-coordinator-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-group-coordinator/3.9.1/kafka-group-coordinator-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-transaction-coordinator/3.9.1/kafka-transaction-coordinator-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-metadata/3.9.1/kafka-metadata-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-storage-api/3.9.1/kafka-storage-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-tools-api/3.9.1/kafka-tools-api-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-raft/3.9.1/kafka-raft-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-storage/3.9.1/kafka-storage-3.9.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-server/3.9.1/kafka-server-3.9.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/argparse4j/argparse4j/0.7.0/argparse4j-0.7.0.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.13/2.19.0/jackson-module-scala_2.13-2.19.0.jar:/Users/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.8.3/paranamer-2.8.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-csv/2.19.0/jackson-dataformat-csv-2.19.0.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/Users/<USER>/.m2/repository/org/bitbucket/b_c/jose4j/0.9.4/jose4j-0.9.4.jar:/Users/<USER>/.m2/repository/com/yammer/metrics/metrics-core/2.2.0/metrics-core-2.2.0.jar:/Users/<USER>/.m2/repository/org/scala-lang/modules/scala-collection-compat_2.13/2.10.0/scala-collection-compat_2.13-2.10.0.jar:/Users/<USER>/.m2/repository/org/scala-lang/modules/scala-java8-compat_2.13/1.0.2/scala-java8-compat_2.13-1.0.2.jar:/Users/<USER>/.m2/repository/com/typesafe/scala-logging/scala-logging_2.13/3.9.5/scala-logging_2.13-3.9.5.jar:/Users/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.1.12.1/metrics-core-4.1.12.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/3.7.2/kafka-clients-3.7.2.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.7/snappy-java-1.1.10.7.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-streams/3.7.2/kafka-streams-3.7.2.jar:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.5.0/quartz-2.5.0.jar:/Users/<USER>/.m2/repository/com/hankcs/hanlp/portable-1.5.3/hanlp-portable-1.5.3.jar:/Users/<USER>/.m2/repository/com/hankcs/nlp/hanlp-lucene-plugin/1.1.2/hanlp-lucene-plugin-1.1.2.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-common/9.12.1/lucene-analysis-common-9.12.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/9.12.1/lucene-queryparser-9.12.1.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar:/Users/<USER>/.m2/repository/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar:/Users/<USER>/.m2/repository/com/vividsolutions/jts/1.13/jts-1.13.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/3.2.0/caffeine-3.2.0.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/com/github/os72/protobuf-dynamic/1.0.1/protobuf-dynamic-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/4.29.3/protobuf-java-util-4.29.3.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/4.29.3/protobuf-java-4.29.3.jar:/Users/<USER>/.m2/repository/org/voovan/voovan-framework/4.3.8/voovan-framework-4.3.8.jar:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm-sdk/target/classes:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_httpserver/0.16.0/simpleclient_httpserver-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.16.0/simpleclient_common-0.16.0.jar:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_hotspot/0.16.0/simpleclient_hotspot-0.16.0.jar:/Users/<USER>/.m2/repository/com/github/oshi/oshi-core/5.8.3/oshi-core-5.8.3.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna-platform/5.10.0/jna-platform-5.10.0.jar:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/plugin-kit/tapdata-common/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm-common/target/classes:/Users/<USER>/.m2/repository/com/tapdata/common/2.2.2/common-2.2.2.jar:/Users/<USER>/.m2/repository/ognl/ognl/3.1.26/ognl-3.1.26.jar:/Users/<USER>/.m2/repository/io/github/openlg/graphlib/1.1.0/graphlib-1.1.0.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-crypto/5.8.36/hutool-crypto-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-json/5.8.36/hutool-json-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-extra/5.8.36/hutool-extra-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-setting/5.8.36/hutool-setting-5.8.36.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-log/5.8.36/hutool-log-5.8.36.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-core/5.8.36/hutool-core-5.8.36.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.25/kotlin-stdlib-common-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.9.25/kotlin-stdlib-jdk8-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.9.25/kotlin-stdlib-jdk7-1.9.25.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/2.1.10/kotlin-stdlib-2.1.10.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.13.0/commons-text-1.13.0.jar:/Users/<USER>/.m2/repository/com/hazelcast/hazelcast/5.5.0/hazelcast-5.5.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-xml/2.18.3/jackson-dataformat-xml-2.18.3.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/4.2/stax2-api-4.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/7.1.0/woodstox-core-7.1.0.jar:/Users/<USER>/.m2/repository/com/hazelcast/hazelcast-persistence/5.5.0-SNAPSHOT/hazelcast-persistence-5.5.0-20250513.112933-3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/mapdb/mapdb/3.0.8/mapdb-3.0.8.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-api/12.0.0.M20.JDK17/eclipse-collections-api-12.0.0.M20.JDK17.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections/12.0.0.M20.JDK17/eclipse-collections-12.0.0.M20.JDK17.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-forkjoin/12.0.0.M20.JDK17/eclipse-collections-forkjoin-12.0.0.M20.JDK17.jar:/Users/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar:/Users/<USER>/.m2/repository/org/mapdb/elsa/3.0.0-M5/elsa-3.0.0-M5.jar:/Users/<USER>/.m2/repository/it/unimi/dsi/fastutil/8.5.13/fastutil-8.5.13.jar:/Users/<USER>/.m2/repository/org/openjdk/nashorn/nashorn-core/15.6/nashorn-core-15.6.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/7.1/asm-7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-commons/7.3.1/asm-commons-7.3.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-analysis/7.3.1/asm-analysis-7.3.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-tree/7.1/asm-tree-7.1.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm-util/7.3.1/asm-util-7.3.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.4.4/httpclient5-5.4.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.3.4/httpcore5-5.3.4.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.3.4/httpcore5-h2-5.3.4.jar:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/validator/target/classes:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.5/commons-csv-1.5.jar:/Users/<USER>/.m2/repository/org/simplejavamail/simple-java-mail/5.0.3/simple-java-mail-5.0.3.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.6.0/javax.mail-1.6.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/com/github/bbottema/emailaddress-rfc2822/1.0.1/emailaddress-rfc2822-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.1/jsr305-3.0.1.jar:/Users/<USER>/.m2/repository/org/samba/jcifs/jcifs/1.3.17/jcifs-1.3.17.jar:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/error-code-root/error-code-core/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/error-code-root/pdk-error-code/target/classes:/Users/<USER>/IdeaProjects/tapdata_v3/tapdata-common-lib/error-code-root/error-code-scanner/target/classes:/Users/<USER>/.m2/repository/org/reflections/reflections/0.10.2/reflections-0.10.2.jar:/Users/<USER>/.m2/repository/org/javassist/javassist/3.28.0-GA/javassist-3.28.0-GA.jar:/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/iengine/modules/task-resource-supervisor-module/target/classes:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/9.12.1/lucene-core-9.12.1.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar.
 Agent version: -
[INFO ] 2025-06-19 12:23:05.443  [main] ConnectorManager - Login params: accessCode=3324cfdf-7d3e-4792-bd32-571638d4562f, endpoint=[http://localhost:3000/api/]
[INFO ] 2025-06-19 12:23:05.492  [main] Version - flow engine version: 
[WARN ] 2025-06-19 12:23:05.526  [main] RestTemplateOperator - RestApi '4f653483-f38e-4284-8d82-68ba9044e39a' failed, use 83ms, retryTime 48000ms, retryInterval 500ms, reqURL: http://localhost:3000/api/users/generatetoken, reqParams: null, error message: I/O error on POST request for "http://localhost:3000/api/users/generatetoken": Connect to http://localhost:3000 [localhost/127.0.0.1] failed: Connection refused
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "http://localhost:3000/api/users/generatetoken": Connect to http://localhost:3000 [localhost/127.0.0.1] failed: Connection refused
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:801) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:683) ~[spring-web-6.2.2.jar:6.2.2]
	at com.tapdata.mongo.RestTemplateOperator.lambda$postOne$6(RestTemplateOperator.java:308) ~[classes/:?]
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:717) ~[classes/:?]
	at com.tapdata.mongo.RestTemplateOperator.postOne(RestTemplateOperator.java:300) ~[classes/:?]
	at com.tapdata.mongo.RestTemplateOperator.postOne(RestTemplateOperator.java:263) ~[classes/:?]
	at io.tapdata.Schedule.ConnectorManager.login(ConnectorManager.java:476) ~[classes/:?]
	at io.tapdata.Schedule.ConnectorManager.init(ConnectorManager.java:214) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) [spring-beans-6.2.7.jar:6.2.7]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) [spring-context-6.2.7.jar:6.2.7]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) [spring-context-6.2.7.jar:6.2.7]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) [spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) [spring-boot-3.5.0.jar:3.5.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-3.5.0.jar:3.5.0]
	at io.tapdata.Application.main(Application.java:133) [classes/:?]
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:3000 [localhost/127.0.0.1] failed: Connection refused
	at sun.nio.ch.Net.pollConnect(Native Method) ~[?:?]
	at sun.nio.ch.Net.pollConnectNow(Net.java:672) ~[?:?]
	at sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554) ~[?:?]
	at sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602) ~[?:?]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.net.Socket.connect(Socket.java:639) ~[?:?]
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:216) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:490) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:164) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:174) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:144) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:150) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:110) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:183) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183) ~[httpclient5-5.4.4.jar:5.4.4]
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:71) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:117) ~[spring-web-6.2.2.jar:6.2.2]
	at com.tapdata.tm.sdk.interceptor.VersionHeaderInterceptor.intercept(VersionHeaderInterceptor.java:26) ~[classes/:?]
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88) ~[spring-web-6.2.2.jar:6.2.2]
	at com.tapdata.interceptor.LoggingInterceptor.intercept(LoggingInterceptor.java:33) ~[classes/:?]
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:81) ~[spring-web-6.2.2.jar:6.2.2]
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:900) ~[spring-web-6.2.2.jar:6.2.2]
	... 32 more
[INFO ] 2025-06-19 12:23:06.249  [main] RestTemplateOperator - RestApi '4f653483-f38e-4284-8d82-68ba9044e39a' completed, use 806ms, retries 1
[INFO ] 2025-06-19 12:23:06.725  [main] SettingService - [Setting] - Loading tapdata settings...
[java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.common.SettingService.loadSettings(SettingService.java:40), io.tapdata.Schedule.ConnectorManager.init(ConnectorManager.java:216), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method), java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401), org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:429), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1818), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607), org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529), org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339), org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373), org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337), org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202), org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188), org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123), org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987), org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627), org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753), org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439), org.springframework.boot.SpringApplication.run(SpringApplication.java:318), io.tapdata.Application.main(Application.java:133)]
[INFO ] 2025-06-19 12:23:07.395  [main] TransformerManager - Transformer init variables
 - process id: zed_flow_engine
 - job tags: 
 - region: 
 - zone: 
 - worker dir: null
[INFO ] 2025-06-19 12:23:07.442  [main] HazelcastUtil - -- listing properties --
hazelcast.operation.call.timeout.millis=300000
hazelcast.logging.type=log4j2

[INFO ] 2025-06-19 12:23:07.493  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Interfaces is disabled, trying to pick one address from TCP-IP config addresses: []
[WARN ] 2025-06-19 12:23:07.494  [main] AddressPicker - [LOCAL] [dev] [5.5.0] Could not find a matching address to start with! Picking one of non-loopback addresses.
[INFO ] 2025-06-19 12:23:07.534  [main] logo - [**********]:5701 [dev] [5.5.0] 
	+       +  o    o     o     o---o o----o o      o---o     o     o----o o--o--o
	+ +   + +  |    |    / \       /  |      |     /         / \    |         |   
	+ + + + +  o----o   o   o     o   o----o |    o         o   o   o----o    |   
	+ +   + +  |    |  /     \   /    |      |     \       /     \       |    |   
	+       +  o    o o       o o---o o----o o----o o---o o       o o----o    o   
[INFO ] 2025-06-19 12:23:07.536  [main] system - [**********]:5701 [dev] [5.5.0] Copyright (c) 2008-2022, Hazelcast, Inc. All Rights Reserved.
[INFO ] 2025-06-19 12:23:07.536  [main] system - [**********]:5701 [dev] [5.5.0] Hazelcast Platform 5.5.0 (20250304 - 01f9bbc) starting at [**********]:5701
[INFO ] 2025-06-19 12:23:07.536  [main] system - [**********]:5701 [dev] [5.5.0] Cluster name: dev
[INFO ] 2025-06-19 12:23:07.537  [main] system - [**********]:5701 [dev] [5.5.0] Integrity Checker is disabled. Fail-fast on corrupted executables will not be performed. For more information, see the documentation for Integrity Checker.
[INFO ] 2025-06-19 12:23:07.537  [main] system - [**********]:5701 [dev] [5.5.0] Jet is enabled
[INFO ] 2025-06-19 12:23:07.799  [main] security - [**********]:5701 [dev] [5.5.0] Enable DEBUG/FINE log level for log category com.hazelcast.system.security  or use -Dhazelcast.security.recommendations system property to see 🔒 security recommendations and the status of current config.
[INFO ] 2025-06-19 12:23:07.839  [main] Node - [**********]:5701 [dev] [5.5.0] Using TCP/IP discovery
[WARN ] 2025-06-19 12:23:07.840  [main] CPSubsystem - [**********]:5701 [dev] [5.5.0] CP Subsystem is not enabled. CP data structures will operate in UNSAFE mode! Please note that UNSAFE mode will not provide strong consistency guarantees.
[INFO ] 2025-06-19 12:23:08.004  [main] JetServiceBackend - [**********]:5701 [dev] [5.5.0] Setting number of cooperative threads and default parallelism to 12
[INFO ] 2025-06-19 12:23:08.009  [main] Diagnostics - [**********]:5701 [dev] [5.5.0] Diagnostics disabled. To enable add -Dhazelcast.diagnostics.enabled=true to the JVM arguments.
[INFO ] 2025-06-19 12:23:08.012  [main] LifecycleService - [**********]:5701 [dev] [5.5.0] [**********]:5701 is STARTING
[INFO ] 2025-06-19 12:23:08.026  [main] ClusterService - [**********]:5701 [dev] [5.5.0] 

Members {size:1, ver:1} [
	Member [**********]:5701 - 38c8538c-c6bc-42b2-9a7d-409909039095 this
]

[INFO ] 2025-06-19 12:23:08.032  [main] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Jet started scanning for jobs
[INFO ] 2025-06-19 12:23:08.034  [main] LifecycleService - [**********]:5701 [dev] [5.5.0] [**********]:5701 is STARTED
[INFO ] 2025-06-19 12:23:08.036  [main] TapdataTaskScheduler - [Task scheduler] instance no: zed_flow_engine
[WARN ] 2025-06-19 12:23:08.046  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[WARN ] 2025-06-19 12:23:08.047  [main] CustomConversions - Registering converter from interface java.util.List to interface org.springframework.data.domain.Vector as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
[INFO ] 2025-06-19 12:23:08.102  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 12:23:08.185  [main] Application - Started Application in 4.128 seconds (process running for 4.816)
[INFO ] 2025-06-19 12:23:08.188  [main] PDK - Application [Looking for Aspect annotations...]
[INFO ] 2025-06-19 12:23:08.236  [main] PDK - Application [Looking for Aspect annotations takes 48]
[INFO ] 2025-06-19 12:23:08.237  [main] StartResultUtil - Write start result to file: .agentStartMsg
  {"msg":"","codeVersion":"2025-06-17T06:38:53Z","jvmZoneId":"Asia/Shanghai","osZoneId":"Asia/Shanghai","version":"-","status":"ok"}
[WARN ] 2025-06-19 12:23:08.834  [main] PDK - ApplicationStartAspectHandler [Can not load python engine, msg: Cannot invoke "javax.script.ScriptEngine.eval(String)" because "scriptEnginePy" is null]
[INFO ] 2025-06-19 12:23:08.835  [main] TapdataTaskScheduler - Stop task which agent id is zed_flow_engine and status is stopping
[INFO ] 2025-06-19 12:23:08.850  [main] ConnectorNodeService - Global connector thread pool started, interval ms: 300000, timeout ms: 1800000
[INFO ] 2025-06-19 12:29:06.606  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:29:06.606  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 12:35:06.599  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:35:06.599  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 12:40:06.628  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:40:06.628  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[WARN ] 2025-06-19 12:46:51.721  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 368726 ms
[INFO ] 2025-06-19 12:46:51.721  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 12:40:37.964 to 2025-06-19 12:46:51.721 since last heartbeat (+368757 ms)
[WARN ] 2025-06-19 12:46:51.721  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 368757 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 12:46:59.903  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 12:47:08.386  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 12:47:09.915  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 12:47:09.916  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 12:47:15.325  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 12:47:15.325  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:47:18.498  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 12:47:18.499  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 12:55:58.920  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 484244 ms
[INFO ] 2025-06-19 12:56:00.887  [hz.zed_flow_engine.cached.thread-5] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 12:47:51.645 to 2025-06-19 12:56:00.886 since last heartbeat (+484241 ms)
[WARN ] 2025-06-19 12:56:00.888  [hz.zed_flow_engine.cached.thread-5] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 484241 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 12:56:04.163  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[WARN ] 2025-06-19 12:56:05.912  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 852927 ms
[INFO ] 2025-06-19 12:56:13.329  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 12:56:14.173  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 12:56:14.174  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 12:56:19.573  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 12:56:19.573  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 12:56:23.438  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 12:56:23.439  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:05:22.931  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 334015 ms
[INFO ] 2025-06-19 13:05:23.277  [WebSocketClient-AsyncIO-8] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:05:23.407  [nioEventLoopGroup-3-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 13:05:23.407  [nioEventLoopGroup-3-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 13:05:23.407  [nioEventLoopGroup-3-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 13:05:23.715  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 13:05:23.729  [ForkJoinPool.commonPool-worker-12] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIxOTAzNzc3ODM0Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lXzRkNjU2NjA0MDY5ZTQwMzNhNzc2NDJmYjViNDJjMDNkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDMwOTU1MywiaWF0IjoxNzUwMzA5NTIzfQ.4Ie0nio7XYig-mo3RU4JJagjdJVwfxWQUewLgpZTIA0]
[INFO ] 2025-06-19 13:05:23.731  [ForkJoinPool.commonPool-worker-12] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/29aada6d79d9b71046bbf0a3502a1b73 wsPort 8246]
[INFO ] 2025-06-19 13:05:23.738  [ForkJoinPool.commonPool-worker-12] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 13:05:23.738  [ForkJoinPool.commonPool-worker-12] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIxOTAzNzc3ODM0Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lXzRkNjU2NjA0MDY5ZTQwMzNhNzc2NDJmYjViNDJjMDNkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDMwOTU1MywiaWF0IjoxNzUwMzA5NTIzfQ.4Ie0nio7XYig-mo3RU4JJagjdJVwfxWQUewLgpZTIA0]
[INFO ] 2025-06-19 13:05:23.740  [nioEventLoopGroup-5-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 13:05:23.740  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:05:24.904  [hz.zed_flow_engine.cached.thread-13] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 12:59:45.886 to 2025-06-19 13:05:24.903 since last heartbeat (+334017 ms)
[WARN ] 2025-06-19 13:05:24.905  [hz.zed_flow_engine.cached.thread-13] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 334017 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 13:05:24.936  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 334016 ms
[INFO ] 2025-06-19 13:05:29.911  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:05:33.287  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:05:33.287  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:05:40.033  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:05:40.037  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:14:33.291  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 465348 ms
[INFO ] 2025-06-19 13:14:35.268  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:06:44.909 to 2025-06-19 13:14:35.267 since last heartbeat (+465358 ms)
[WARN ] 2025-06-19 13:14:35.268  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 465358 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 13:14:38.676  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:14:38.970  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 13:14:38.970  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:14:46.306  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:14:48.689  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:14:48.689  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:14:56.424  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:14:56.424  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:15:50.099  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 28803 ms
[WARN ] 2025-06-19 13:16:09.102  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 494158 ms
[WARN ] 2025-06-19 13:23:47.024  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 429929 ms
[INFO ] 2025-06-19 13:23:47.446  [WebSocketClient-AsyncIO-6] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:23:47.495  [nioEventLoopGroup-5-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 13:23:47.495  [nioEventLoopGroup-5-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 13:23:47.496  [nioEventLoopGroup-5-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 13:23:47.803  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[INFO ] 2025-06-19 13:23:47.851  [ForkJoinPool.commonPool-worker-13] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIxOTAzNzc3ODM0Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lXzRkNjU2NjA0MDY5ZTQwMzNhNzc2NDJmYjViNDJjMDNkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDMxMDY1NywiaWF0IjoxNzUwMzEwNjI3fQ.E7EK8GwXfZxds1kuvsgchnIKWoTRrbA9HCMrebEdkVQ]
[INFO ] 2025-06-19 13:23:47.852  [ForkJoinPool.commonPool-worker-13] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/29aada6d79d9b71046bbf0a3502a1b73 wsPort 8246]
[INFO ] 2025-06-19 13:23:47.855  [ForkJoinPool.commonPool-worker-13] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 13:23:47.855  [ForkJoinPool.commonPool-worker-13] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.eyJub2RlSWQiOiIxOTAzNzc3ODM0Iiwic2VydmljZSI6ImVuZ2luZSIsImNsaWVudElkIjoiemVkX2Zsb3dfZW5naW5lXzRkNjU2NjA0MDY5ZTQwMzNhNzc2NDJmYjViNDJjMDNkIiwidGVybWluYWwiOjEsInVpZCI6IjYyYmM1MDA4ZDQ5NThkMDEzZDk3YzdhNiIsImV4cCI6MTc1MDMxMDY1NywiaWF0IjoxNzUwMzEwNjI3fQ.E7EK8GwXfZxds1kuvsgchnIKWoTRrbA9HCMrebEdkVQ]
[INFO ] 2025-06-19 13:23:47.857  [nioEventLoopGroup-7-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 13:23:47.859  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:23:48.995  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:16:34.070 to 2025-06-19 13:23:48.994 since last heartbeat (+429924 ms)
[WARN ] 2025-06-19 13:23:48.995  [hz.zed_flow_engine.cached.thread-6] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 429924 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 13:23:56.080  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:23:57.449  [WebSocketClient-AsyncIO-6] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:23:57.450  [WebSocketClient-AsyncIO-6] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:24:06.185  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:24:06.186  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:24:34.023  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 429922 ms
[WARN ] 2025-06-19 13:32:56.728  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 463715 ms
[INFO ] 2025-06-19 13:32:57.649  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:25:08.990 to 2025-06-19 13:32:57.648 since last heartbeat (+463658 ms)
[WARN ] 2025-06-19 13:32:57.649  [hz.zed_flow_engine.cached.thread-10] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 463658 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 13:33:01.139  [WebSocketClient-AsyncIO-12] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:33:01.143  [nioEventLoopGroup-7-1] PDK - MonitorThread [status changed, ChannelStatus disconnected null null]
[INFO ] 2025-06-19 13:33:01.143  [nioEventLoopGroup-7-1] PDK - MonitorThread [MonitorThread restart channel, no hurry]
[INFO ] 2025-06-19 13:33:01.143  [nioEventLoopGroup-7-1] PDK - WebsocketPushChannel [stopped]
[INFO ] 2025-06-19 13:33:01.366  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 13:33:01.366  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:33:01.453  [MonitorThread] PDK - WebsocketPushChannel [PushChannel started]
[WARN ] 2025-06-19 13:33:01.454  [MonitorThread] PDK - WebsocketPushChannel [Channel not initialized before sending data, IncomingData id DATA_9 ContentData ContentType NodeSubscribeInfo ContentEncode 20 message NodeSubscribeInfo subscribeIds [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:33:01.463  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Login successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************.7qS_SCBhAkTNOUbgkAOP7OKPZUxUdbjJ9hDAqSRWKA0]
[INFO ] 2025-06-19 13:33:01.465  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [Connect uri ws://localhost:8246/engine/29aada6d79d9b71046bbf0a3502a1b73 wsPort 8246]
[INFO ] 2025-06-19 13:33:01.471  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [connectWS: sendIdentityContentData ContentType null ContentEncode null message null]
[INFO ] 2025-06-19 13:33:01.471  [ForkJoinPool.commonPool-worker-14] PDK - WebsocketPushChannel [WS connected successfully, localhost 8246 null eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************.7qS_SCBhAkTNOUbgkAOP7OKPZUxUdbjJ9hDAqSRWKA0]
[INFO ] 2025-06-19 13:33:01.473  [nioEventLoopGroup-9-1] PDK - MonitorThread [status changed, ChannelStatus connected null null]
[INFO ] 2025-06-19 13:33:01.875  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:33:10.772  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:33:11.145  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:33:11.146  [WebSocketClient-AsyncIO-12] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:33:20.884  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:33:20.884  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:33:32.673  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 463650 ms
[WARN ] 2025-06-19 13:42:02.727  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 499049 ms
[INFO ] 2025-06-19 13:42:04.997  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:42:06.485  [hz.zed_flow_engine.cached.thread-5] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:33:42.653 to 2025-06-19 13:42:06.484 since last heartbeat (+498831 ms)
[WARN ] 2025-06-19 13:42:06.486  [hz.zed_flow_engine.cached.thread-5] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 498831 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 13:42:10.074  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:42:15.008  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:42:15.009  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:42:20.180  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:42:20.181  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:42:20.211  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 13:42:20.211  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:51:09.739  [WebSocketClient-AsyncIO-8] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[WARN ] 2025-06-19 13:51:10.247  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 484731 ms
[INFO ] 2025-06-19 13:51:11.214  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:43:01.491 to 2025-06-19 13:51:11.213 since last heartbeat (+484722 ms)
[WARN ] 2025-06-19 13:51:11.214  [hz.zed_flow_engine.cached.thread-15] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 484722 ms, Heartbeat-Timeout: 60000 ms
[WARN ] 2025-06-19 13:51:11.239  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 983565 ms
[INFO ] 2025-06-19 13:51:15.489  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:51:19.744  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:51:19.744  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:51:24.945  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 13:51:24.945  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 13:51:25.603  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:51:25.603  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 13:58:00.058  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 362812 ms
[INFO ] 2025-06-19 13:58:04.032  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 13:51:56.213 to 2025-06-19 13:58:04.032 since last heartbeat (+362819 ms)
[WARN ] 2025-06-19 13:58:04.032  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 362819 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 13:58:12.570  [WebSocketClient-AsyncIO-8] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 13:58:19.002  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 13:58:22.572  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:58:22.573  [WebSocketClient-AsyncIO-8] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 13:58:27.759  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 13:58:27.759  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[WARN ] 2025-06-19 13:58:29.054  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 362816 ms
[INFO ] 2025-06-19 13:58:29.111  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 13:58:29.112  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 14:00:58.244  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 105192 ms
[WARN ] 2025-06-19 14:00:58.896  [hz.zed_flow_engine.cached.thread-7] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 104872 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 14:01:12.463  [WebSocketClient-AsyncIO-10] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 14:01:14.646  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 14:01:22.468  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 14:01:22.468  [WebSocketClient-AsyncIO-10] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 14:01:24.753  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 14:01:24.754  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 14:01:28.928  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 104874 ms
[INFO ] 2025-06-19 14:02:16.602  [Thread-websocket-handle-message--2-thread-24] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"null","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:02:16.624  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[ERROR] 2025-06-19 14:02:16.644  [DOWNLOAD-CONNECTOR-sqlserver_ad] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata_v3/dist/mssql-connector-v1.0-SNAPSHOT__68493f730609ea0cc8a6dc46__.jar to /Users/<USER>/IdeaProjects/tapdata_v3/connectors/tap-running/mssql-connector-v1.0-SNAPSHOT__68493f730609ea0cc8a6dc46___2a83aa2d-a02e-4596-806f-9f8e0b92003f.jar failed]
[INFO ] 2025-06-19 14:02:16.702  [Thread-websocket-handle-message--2-thread-1] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"null","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"testConnection"}
[INFO ] 2025-06-19 14:02:16.702  [Thread-56] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:02:27.094  [Thread-websocket-handle-message--2-thread-3] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"null","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:02:27.103  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:02:27.118  [Thread-websocket-handle-message--2-thread-4] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"null","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"testConnection"}
[INFO ] 2025-06-19 14:02:27.119  [Thread-57] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:02:33.793  [Thread-websocket-handle-message--2-thread-5] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"null","type":"testConnection"}
[INFO ] 2025-06-19 14:02:33.793  [Thread-58] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:02:35.461  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Starting load schema fields, connection name: sqlserver_ad
[INFO ] 2025-06-19 14:02:36.865  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Finished load schema fields, connection name: sqlserver_ad, progress: 1/1
[INFO ] 2025-06-19 14:03:10.573  [Thread-websocket-handle-message--2-thread-10] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a80551463b1f7959acc7, opType=start, force=false}
[INFO ] 2025-06-19 14:03:10.574  [Thread-websocket-handle-message--2-thread-10] TapdataTaskScheduler - Send start task operation: 任务 17[6853a80551463b1f7959acc7]
[INFO ] 2025-06-19 14:03:10.574  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 17, task id 6853a80551463b1f7959acc7
[INFO ] 2025-06-19 14:03:10.574  [Thread-websocket-handle-message--2-thread-10] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a80551463b1f7959acc7, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:03:10.706  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:03:10.715  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] AppenderFactory - Delete chronic released store file: ./CacheObserveLogs/20250617H.cq4, success: true. cycle: 20256
[WARN ] 2025-06-19 14:03:10.716  [Read_TM_Appender_TAILER_Thread] AppenderFactory - failed to append task logs, error: missing currentCycle, file=./CacheObserveLogs/20250617H.cq4
java.lang.AssertionError: missing currentCycle, file=./CacheObserveLogs/20250617H.cq4
	at net.openhft.chronicle.queue.impl.single.SingleChronicleQueue$StoreSupplier.nextCycle(SingleChronicleQueue.java:1213) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.WireStorePool.nextCycle(WireStorePool.java:66) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.SingleChronicleQueue.nextCycle(SingleChronicleQueue.java:535) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.nextIndexWithNextAvailableCycle(StoreTailer.java:553) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.endOfCycle(StoreTailer.java:360) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.next0(StoreTailer.java:332) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readingDocument0(StoreTailer.java:230) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readingDocument(StoreTailer.java:196) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readDocument(StoreTailer.java:122) ~[chronicle-queue-5.21.91.jar:?]
	at io.tapdata.observable.logging.appender.AppenderFactory.readMessageFromCacheQueue(AppenderFactory.java:94) ~[classes/:?]
	at io.tapdata.observable.logging.appender.AppenderFactory.lambda$new$3(AppenderFactory.java:85) ~[classes/:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
[INFO ] 2025-06-19 14:03:10.734  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] AutoRecovery - Init auto-recovery instance '6853a80551463b1f7959acc7'
[INFO ] 2025-06-19 14:03:10.746  [6853a80551463b1f7959acc7-TaskInspect-high-priority-checker] AbsInnerJob - start
[INFO ] 2025-06-19 14:03:10.921  [Read_TM_Appender_TAILER_Thread] AppenderFactory - Delete chronic released store file: ./CacheObserveLogs/20250617H.cq4, success: false. cycle: 20256
[WARN ] 2025-06-19 14:03:10.966  [Read_File_Appender_TAILER_Thread] AppenderFactory - failed to append task logs, error: Expected file to exist for cycle: 20256, file: ./CacheObserveLogs/20250617H.cq4.
minCycle: 20258, maxCycle: 20258
Available files: [20250619H.cq4]
java.lang.IllegalStateException: Expected file to exist for cycle: 20256, file: ./CacheObserveLogs/20250617H.cq4.
minCycle: 20258, maxCycle: 20258
Available files: [20250619H.cq4]
	at net.openhft.chronicle.queue.impl.single.SingleChronicleQueue$StoreSupplier.nextCycle(SingleChronicleQueue.java:1197) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.WireStorePool.nextCycle(WireStorePool.java:66) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.SingleChronicleQueue.nextCycle(SingleChronicleQueue.java:535) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.nextIndexWithNextAvailableCycle(StoreTailer.java:553) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.endOfCycle(StoreTailer.java:360) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.next0(StoreTailer.java:332) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readingDocument0(StoreTailer.java:230) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readingDocument(StoreTailer.java:196) ~[chronicle-queue-5.21.91.jar:?]
	at net.openhft.chronicle.queue.impl.single.StoreTailer.readDocument(StoreTailer.java:122) ~[chronicle-queue-5.21.91.jar:?]
	at io.tapdata.observable.logging.appender.AppenderFactory.readMessageFromCacheQueue(AppenderFactory.java:94) ~[classes/:?]
	at io.tapdata.observable.logging.appender.AppenderFactory.lambda$new$2(AppenderFactory.java:78) ~[classes/:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
[INFO ] 2025-06-19 14:03:10.989  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='GlobalStateMap', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='HazelcastPersistence', exclusiveCollection=false, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:03:11.009  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] ExternalStorageUtil - Init IMap store config succeed, name: GlobalStateMap
[INFO ] 2025-06-19 14:03:11.028  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] PartitionStateManager - [**********]:5701 [dev] [5.5.0] Initializing cluster partition table arrangement...
[INFO ] 2025-06-19 14:03:11.131  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:03:11.167  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Task initialization... 
[TRACE] 2025-06-19 14:03:11.168  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Start task milestones: 6853a80551463b1f7959acc7(任务 17) 
[INFO ] 2025-06-19 14:03:11.168  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Loading table structure completed 
[TRACE] 2025-06-19 14:03:11.297  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Node performs snapshot read asynchronously 
[INFO ] 2025-06-19 14:03:11.398  [hz.zed_flow_engine.cached.thread-2] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f10a-9880-0001 based on submit request
[TRACE] 2025-06-19 14:03:11.401  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - The engine receives 任务 17 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:03:11.401  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Task started 
[INFO ] 2025-06-19 14:03:11.405  [hz.zed_flow_engine.cached.thread-2] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 17-6853a80551463b1f7959acc7', execution 0db5-f10a-9881-0001
[INFO ] 2025-06-19 14:03:11.405  [hz.zed_flow_engine.cached.thread-2] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 17-6853a80551463b1f7959acc7', execution 0db5-f10a-9881-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver -ag1-a1ba2f1d-4008-40e6-b520-1a74bc820105" [localParallelism=1];
	"sqlserver_ad-698c9c92-ab4d-40e5-a06c-820dd84543f3" [localParallelism=1];
	"sqlserver -ag1-a1ba2f1d-4008-40e6-b520-1a74bc820105" -> "sqlserver_ad-698c9c92-ab4d-40e5-a06c-820dd84543f3" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:03:11.401  [Start-Task-Operation-Handler-任务 17[6853a80551463b1f7959acc7]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@4f21ae93, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@644e1d5a, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853a81e51463b1f7959ad15, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750312990482, stopedDate=null, needFilterEventData=null, testTaskId=6853a80551463b1f7959acc5, transformTaskId=6853a80551463b1f7959acc6, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:03:11.443  [hz.zed_flow_engine.cached.thread-2] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f10a-9880-0001, jobName='任务 17-6853a80551463b1f7959acc7', executionId=0db5-f10a-9881-0001 initialized
[INFO ] 2025-06-19 14:03:11.445  [hz.zed_flow_engine.cached.thread-2] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 17-6853a80551463b1f7959acc7', execution 0db5-f10a-9881-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:03:11.447  [hz.zed_flow_engine.jet.blocking.thread-1] ExternalStorageUtil - Node sqlserver_ad(id: 698c9c92-ab4d-40e5-a06c-820dd84543f3, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:03:11.447  [hz.zed_flow_engine.jet.blocking.thread-0] ExternalStorageUtil - Node sqlserver -ag1(id: a1ba2f1d-4008-40e6-b520-1a74bc820105, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:03:11.454  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] start preload schema,table counts: 3 
[TRACE] 2025-06-19 14:03:11.455  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] start preload schema,table counts: 3 
[TRACE] 2025-06-19 14:03:11.455  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:03:11.455  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:03:11.455  [HazelcastBaseNode-任务 17(6853a80551463b1f7959acc7)-sqlserver -ag1(a1ba2f1d-4008-40e6-b520-1a74bc820105)] AutoRecovery - Set auto-recovery enqueue: '6853a80551463b1f7959acc7-6853a80551463b1f7959acc7'
[INFO ] 2025-06-19 14:03:11.459  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]@task-任务 17-4-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1375366364', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1375366364', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:03:11.532  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]@task-任务 17-4-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1375366364
[INFO ] 2025-06-19 14:03:11.532  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-1464879879', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-1464879879', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:03:11.603  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -1464879879
[INFO ] 2025-06-19 14:03:11.604  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]@task-任务 17-4-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_698c9c92-ab4d-40e5-a06c-820dd84543f3', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_698c9c92-ab4d-40e5-a06c-820dd84543f3', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:03:11.671  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]@task-任务 17-4-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_698c9c92-ab4d-40e5-a06c-820dd84543f3
[INFO ] 2025-06-19 14:03:11.671  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_a1ba2f1d-4008-40e6-b520-1a74bc820105', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_a1ba2f1d-4008-40e6-b520-1a74bc820105', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:03:11.737  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_a1ba2f1d-4008-40e6-b520-1a74bc820105
[INFO ] 2025-06-19 14:03:11.761  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]@task-任务 17-4-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] complete | Associate id: HazelcastTargetPdkDataNode_698c9c92-ab4d-40e5-a06c-820dd84543f3_1750312991720
[INFO ] 2025-06-19 14:03:11.762  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:03:11.782  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] complete | Associate id: HazelcastSourcePdkDataNode_a1ba2f1d-4008-40e6-b520-1a74bc820105_1750312991755
[INFO ] 2025-06-19 14:03:12.054  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:03:12.055  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:03:12.055  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:03:12.226  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Apply table structure to target database 
[TRACE] 2025-06-19 14:03:12.226  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:03:12.266  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#682169965b36c31a1fe9e6c4, target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:03:33.297  [Thread-websocket-handle-message--2-thread-13] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a80551463b1f7959acc7, opType=stop, force=false}
[INFO ] 2025-06-19 14:03:33.297  [Thread-websocket-handle-message--2-thread-13] TapdataTaskScheduler - Send stop task operation: 6853a80551463b1f7959acc7
[INFO ] 2025-06-19 14:03:33.297  [Thread-websocket-handle-message--2-thread-13] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a80551463b1f7959acc7, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:03:33.300  [Stop-Task-Operation-Handler-6853a80551463b1f7959acc7] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a80551463b1f7959acc7'} TaskOperation{, opType=STOP}
[WARN ] 2025-06-19 14:03:36.152  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[WARN ] 2025-06-19 14:03:41.159  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[WARN ] 2025-06-19 14:03:46.164  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[WARN ] 2025-06-19 14:03:51.172  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:03:55.898  [Thread-websocket-handle-message--2-thread-16] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"682169965b36c31a1fe9e6c4","connectionName":"sqlserver -ag1","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"d752558c-a126-4015-967f-8472364f8a2e","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:03:55.906  [DOWNLOAD-CONNECTOR-sqlserver -ag1] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:03:55.930  [Thread-websocket-handle-message--2-thread-17] TestConnectionHandler - Test connection '***********:1433/AGTest/dbo', entity: {"connectionId":"682169965b36c31a1fe9e6c4","connectionName":"sqlserver -ag1","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"d752558c-a126-4015-967f-8472364f8a2e","type":"testConnection"}
[INFO ] 2025-06-19 14:03:55.931  [Thread-61] TestConnectionHandler - Starting validate connections name: sqlserver -ag1.
[WARN ] 2025-06-19 14:03:56.177  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:03:59.443  [Thread-websocket-handle-message--2-thread-19] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"682169985b36c31a1fe9e6c9","connectionName":"sqlserver - ag2","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"dcbd4539-4304-450b-a99a-84bfe8cbaf06","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:03:59.451  [DOWNLOAD-CONNECTOR-sqlserver - ag2] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:03:59.473  [Thread-websocket-handle-message--2-thread-20] TestConnectionHandler - Test connection '20.2.24.148:1433/AGTest/dbo', entity: {"connectionId":"682169985b36c31a1fe9e6c9","connectionName":"sqlserver - ag2","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"dcbd4539-4304-450b-a99a-84bfe8cbaf06","type":"testConnection"}
[INFO ] 2025-06-19 14:03:59.473  [Thread-62] TestConnectionHandler - Starting validate connections name: sqlserver - ag2.
[WARN ] 2025-06-19 14:04:01.183  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:04:04.029  [Thread-websocket-handle-message--2-thread-21] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"682169985b36c31a1fe9e6c9","connectionName":"sqlserver - ag2","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"dcbd4539-4304-450b-a99a-84bfe8cbaf06","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:04:04.039  [DOWNLOAD-CONNECTOR-sqlserver - ag2] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:04:04.061  [Thread-websocket-handle-message--2-thread-22] TestConnectionHandler - Test connection '20.2.24.148:1433/AGTest/dbo', entity: {"connectionId":"682169985b36c31a1fe9e6c9","connectionName":"sqlserver - ag2","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"dcbd4539-4304-450b-a99a-84bfe8cbaf06","type":"testConnection"}
[INFO ] 2025-06-19 14:04:04.061  [Thread-63] TestConnectionHandler - Starting validate connections name: sqlserver - ag2.
[WARN ] 2025-06-19 14:04:06.189  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[WARN ] 2025-06-19 14:04:11.190  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:04:13.799  [Thread-websocket-handle-message--2-thread-24] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a80551463b1f7959acc7, opType=stop, force=false}
[INFO ] 2025-06-19 14:04:13.799  [Thread-websocket-handle-message--2-thread-24] TapdataTaskScheduler - Send stop task operation: 6853a80551463b1f7959acc7
[INFO ] 2025-06-19 14:04:13.799  [Thread-websocket-handle-message--2-thread-24] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a80551463b1f7959acc7, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:04:13.800  [Stop-Task-Operation-Handler-6853a80551463b1f7959acc7] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a80551463b1f7959acc7'} TaskOperation{, opType=STOP}
[INFO ] 2025-06-19 14:04:15.054  [HazelcastSourcePdkBaseNode_Source-Runner-6853a80551463b1f7959acc7-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]-5-thread-1] PDK - HazelcastPdkBaseNode [methodEnd - INIT | message - (PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e)]
[ERROR] 2025-06-19 14:04:15.066  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization error: Failed to init pdk connector, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sqlserver-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 0): when operate table: unknown, com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:205)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	... 7 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 22 more

[TRACE] 2025-06-19 14:04:15.067  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Exception skipping - The current exception does not match the skip exception strategy, message: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e 
[ERROR] 2025-06-19 14:04:15.088  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e <-- Error Message -->
com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e

<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	...

<-- Full Stack Trace -->
com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:183)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:31)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:73)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.queryTimeZoneOffset(MssqlJdbcRunner.java:95)
	at io.tapdata.connector.mssql.MssqlConnector.onStart(MssqlConnector.java:99)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	... 5 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 通过端口 1433 连接到主机 *********** 的 TCP/IP 连接失败。错误:“驱动程序收到意外的登录前响应。请验证连接属性，并检查 SQL Server 的实例正在主机上运行，且在此端口接受 TCP/IP 连接。该驱动程序只能与 SQL Server 2005 或更高版本一起使用。”。 ClientConnectionId:7fbaabdd-b769-4fb8-8a38-1e606fd1561e
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3153)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 22 more

[TRACE] 2025-06-19 14:04:15.089  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] running status set to false 
[TRACE] 2025-06-19 14:04:15.099  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_a1ba2f1d-4008-40e6-b520-1a74bc820105_1750312991755 
[TRACE] 2025-06-19 14:04:15.100  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_a1ba2f1d-4008-40e6-b520-1a74bc820105_1750312991755 
[TRACE] 2025-06-19 14:04:15.101  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] schema data cleaned 
[TRACE] 2025-06-19 14:04:15.101  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] monitor closed 
[TRACE] 2025-06-19 14:04:15.103  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver -ag1] - Node sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105] close complete, cost 15 ms 
[TRACE] 2025-06-19 14:04:15.103  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] running status set to false 
[TRACE] 2025-06-19 14:04:15.108  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_698c9c92-ab4d-40e5-a06c-820dd84543f3_1750312991720 
[TRACE] 2025-06-19 14:04:15.108  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_698c9c92-ab4d-40e5-a06c-820dd84543f3_1750312991720 
[TRACE] 2025-06-19 14:04:15.108  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] schema data cleaned 
[TRACE] 2025-06-19 14:04:15.109  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] monitor closed 
[TRACE] 2025-06-19 14:04:15.312  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17][sqlserver_ad] - Node sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3] close complete, cost 6 ms 
[WARN ] 2025-06-19 14:04:16.196  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:04:16.221  [6853a80551463b1f7959acc7-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:04:21.211  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:04:21.215  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:04:21.394  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:04:22.222  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a80551463b1f7959acc7'
[INFO ] 2025-06-19 14:04:22.223  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:04:22.223  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a80551463b1f7959acc7
[TRACE] 2025-06-19 14:04:22.223  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@235eb680 
[TRACE] 2025-06-19 14:04:22.224  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6f10a12e 
[INFO ] 2025-06-19 14:04:22.346  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 106 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 14:04:22.348  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Stop task milestones: 6853a80551463b1f7959acc7(任务 17)  
[TRACE] 2025-06-19 14:04:22.348  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Stopped task aspect(s) 
[INFO ] 2025-06-19 14:04:22.349  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 17] status
[TRACE] 2025-06-19 14:04:22.349  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:04:22.350  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Task stopped. 
[INFO ] 2025-06-19 14:04:22.400  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a80551463b1f7959acc7
[TRACE] 2025-06-19 14:04:22.400  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Remove memory task client succeed, task: 任务 17[6853a80551463b1f7959acc7] 
[TRACE] 2025-06-19 14:04:22.400  [Read_File_Appender_TAILER_Thread] job-file-log-6853a80551463b1f7959acc7 - [任务 17] - Destroy memory task client cache succeed, task: 任务 17[6853a80551463b1f7959acc7] 
[INFO ] 2025-06-19 14:04:29.984  [Thread-websocket-handle-message--2-thread-3] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}
[INFO ] 2025-06-19 14:04:29.987  [Thread-websocket-handle-message--2-thread-3] TapdataTaskScheduler - Send start task operation: 任务 18[6853a85b51463b1f7959ad32]
[INFO ] 2025-06-19 14:04:29.987  [Thread-websocket-handle-message--2-thread-3] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:04:29.987  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 18, task id 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:04:30.022  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:04:30.023  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task initialization... 
[INFO ] 2025-06-19 14:04:30.028  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] AutoRecovery - Init auto-recovery instance '6853a85b51463b1f7959ad32'
[INFO ] 2025-06-19 14:04:30.028  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - start
[TRACE] 2025-06-19 14:04:30.079  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:04:30.079  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Loading table structure completed 
[INFO ] 2025-06-19 14:04:30.088  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:04:30.154  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:04:30.154  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:04:30.159  [hz.zed_flow_engine.cached.thread-5] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f10a-9882-0001 based on submit request
[INFO ] 2025-06-19 14:04:30.159  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@2def3f57, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@b376ab3, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853a86d51463b1f7959ad5c, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750313069973, stopedDate=null, needFilterEventData=null, testTaskId=6853a85b51463b1f7959ad30, transformTaskId=6853a85b51463b1f7959ad31, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:04:30.161  [hz.zed_flow_engine.cached.thread-5] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f10a-9883-0001
[INFO ] 2025-06-19 14:04:30.161  [hz.zed_flow_engine.cached.thread-5] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f10a-9883-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" [localParallelism=1];
	"sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [localParallelism=1];
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" -> "sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:04:30.165  [hz.zed_flow_engine.cached.thread-5] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f10a-9882-0001, jobName='任务 18-6853a85b51463b1f7959ad32', executionId=0db5-f10a-9883-0001 initialized
[INFO ] 2025-06-19 14:04:30.165  [hz.zed_flow_engine.cached.thread-5] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f10a-9883-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:04:30.168  [Target-Process-sqlserver_ad[698c9c92-ab4d-40e5-a06c-820dd84543f3]] ExternalStorageUtil - Node sqlserver_ad(id: 55a9af1d-9bf0-4688-a23f-506667cc40db, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:04:30.168  [Source-Complete-sqlserver -ag1[a1ba2f1d-4008-40e6-b520-1a74bc820105]] ExternalStorageUtil - Node sqlserver - ag2(id: 50db2b82-8d28-43d0-af3e-7cd9115522fb, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:04:30.171  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task started 
[TRACE] 2025-06-19 14:04:30.172  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:04:30.172  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:04:30.172  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:04:30.172  [HazelcastBaseNode-任务 18(6853a85b51463b1f7959ad32)-sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb)] AutoRecovery - Set auto-recovery enqueue: '6853a85b51463b1f7959ad32-6853a85b51463b1f7959ad32'
[INFO ] 2025-06-19 14:04:30.176  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-2085252417', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-2085252417', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:04:30.235  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -2085252417
[INFO ] 2025-06-19 14:04:30.235  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-6-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1177672942', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1177672942', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:04:30.293  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-6-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1177672942
[INFO ] 2025-06-19 14:04:30.294  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:04:30.355  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb
[INFO ] 2025-06-19 14:04:30.356  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-6-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[TRACE] 2025-06-19 14:04:30.374  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:04:30.415  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-6-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db
[INFO ] 2025-06-19 14:04:30.448  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] complete | Associate id: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313070415
[INFO ] 2025-06-19 14:04:30.479  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-6-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] complete | Associate id: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313070428
[INFO ] 2025-06-19 14:04:30.523  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#682169985b36c31a1fe9e6c9, target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:04:30.752  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:04:30.752  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:04:30.752  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:04:30.753  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:04:30.815  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:04:30.815  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:04:30.815  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:04:30.945  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-19 14:04:30.945  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:04:30.951  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[TRACE] 2025-06-19 14:04:30.951  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:04:31.138  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:04:31.138  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[INFO ] 2025-06-19 14:04:31.143  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-7-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[TRACE] 2025-06-19 14:04:31.145  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:04:31.145  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:04:31.233  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:04:31.233  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:04:33.972  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:04:33.972  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:04:33.972  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:04:33.973  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:04:33.973  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:04:33.974  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:04:33.974  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[WARN ] 2025-06-19 14:04:34.126  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:04:34.126  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:04:34.327  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:04:34.542  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:04:34.542  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:04:34.747  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:04:37.996  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 2 ms 
[INFO ] 2025-06-19 14:04:37.996  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:04:55.446  [Thread-websocket-handle-message--2-thread-6] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}
[INFO ] 2025-06-19 14:04:55.446  [Thread-websocket-handle-message--2-thread-6] TapdataTaskScheduler - Send stop task operation: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:04:55.446  [Thread-websocket-handle-message--2-thread-6] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:04:55.447  [Stop-Task-Operation-Handler-6853a85b51463b1f7959ad32] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a85b51463b1f7959ad32'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 14:04:55.585  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:04:56.200  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[WARN ] 2025-06-19 14:04:57.428  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[TRACE] 2025-06-19 14:04:58.568  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313070415 
[TRACE] 2025-06-19 14:04:58.568  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313070415 
[TRACE] 2025-06-19 14:04:58.568  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:04:58.569  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:04:58.570  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3013 ms 
[TRACE] 2025-06-19 14:04:58.570  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:04:58.582  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313070428 
[TRACE] 2025-06-19 14:04:58.582  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313070428 
[TRACE] 2025-06-19 14:04:58.583  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:04:58.583  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:04:58.584  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 13 ms 
[WARN ] 2025-06-19 14:05:02.431  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:05:02.730  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:05:07.439  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:05:07.442  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:05:07.442  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:05:08.444  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a85b51463b1f7959ad32'
[TRACE] 2025-06-19 14:05:08.444  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@560317a8 
[INFO ] 2025-06-19 14:05:08.445  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:05:08.445  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:05:08.446  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bfb9cac 
[TRACE] 2025-06-19 14:05:08.446  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[INFO ] 2025-06-19 14:05:08.564  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 106 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 14:05:08.565  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:05:08.566  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:05:08.566  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 18] status
[INFO ] 2025-06-19 14:05:08.566  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task stopped. 
[INFO ] 2025-06-19 14:05:08.616  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[TRACE] 2025-06-19 14:05:08.616  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:05:08.619  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[INFO ] 2025-06-19 14:08:41.065  [Thread-websocket-handle-message--2-thread-6] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"0e2fff85-2d9a-4b0f-9f70-985c939dfa32","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:08:41.073  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:08:41.092  [Thread-websocket-handle-message--2-thread-7] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"0e2fff85-2d9a-4b0f-9f70-985c939dfa32","type":"testConnection"}
[INFO ] 2025-06-19 14:08:41.092  [Thread-67] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:08:46.865  [Thread-websocket-handle-message--2-thread-8] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"0e2fff85-2d9a-4b0f-9f70-985c939dfa32","type":"testConnection"}
[INFO ] 2025-06-19 14:08:46.865  [Thread-68] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:08:48.412  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Starting load schema fields, connection name: sqlserver_ad
[INFO ] 2025-06-19 14:08:49.667  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Finished load schema fields, connection name: sqlserver_ad, progress: 3/3
[INFO ] 2025-06-19 14:10:04.198  [Thread-websocket-handle-message--2-thread-17] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"11d6e4f6-cc0b-449f-9653-d713a3e99b2f","type":"testConnection"}
[INFO ] 2025-06-19 14:10:04.198  [Thread-70] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:10:06.489  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Starting load schema fields, connection name: sqlserver_ad
[INFO ] 2025-06-19 14:10:07.724  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Finished load schema fields, connection name: sqlserver_ad, progress: 3/3
[INFO ] 2025-06-19 14:10:12.665  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:10:12.665  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:10:21.054  [Thread-websocket-handle-message--2-thread-20] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}
[INFO ] 2025-06-19 14:10:21.054  [Thread-websocket-handle-message--2-thread-20] TapdataTaskScheduler - Send start task operation: 任务 19[6853a97351463b1f7959ad8e]
[INFO ] 2025-06-19 14:10:21.054  [Thread-websocket-handle-message--2-thread-20] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:10:21.055  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 19, task id 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:10:21.089  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:10:21.089  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task initialization... 
[INFO ] 2025-06-19 14:10:21.100  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] AutoRecovery - Init auto-recovery instance '6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 14:10:21.100  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - start
[TRACE] 2025-06-19 14:10:21.174  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:10:21.175  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Loading table structure completed 
[INFO ] 2025-06-19 14:10:21.188  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:10:21.241  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:10:21.241  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:10:21.252  [hz.zed_flow_engine.cached.thread-12] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f10a-9884-0001 based on submit request
[INFO ] 2025-06-19 14:10:21.253  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@25c41f70, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@46ef44c0, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853a9cc51463b1f7959adba, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750313421036, stopedDate=null, needFilterEventData=null, testTaskId=6853a97351463b1f7959ad8c, transformTaskId=6853a97351463b1f7959ad8d, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:10:21.255  [hz.zed_flow_engine.cached.thread-12] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f10a-9885-0001
[INFO ] 2025-06-19 14:10:21.255  [hz.zed_flow_engine.cached.thread-12] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f10a-9885-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" [localParallelism=1];
	"local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [localParallelism=1];
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" -> "local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:10:21.260  [hz.zed_flow_engine.cached.thread-12] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f10a-9884-0001, jobName='任务 19-6853a97351463b1f7959ad8e', executionId=0db5-f10a-9885-0001 initialized
[INFO ] 2025-06-19 14:10:21.260  [hz.zed_flow_engine.cached.thread-12] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f10a-9885-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:10:21.261  [hz.zed_flow_engine.jet.blocking.thread-2] ExternalStorageUtil - Node sqlserver_ad(id: 04a3b729-701a-4cf5-93ec-d22f55ff7388, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:10:21.261  [hz.zed_flow_engine.jet.blocking.thread-3] ExternalStorageUtil - Node local_pg(id: db6ac1f3-d782-46d1-9065-615be84ff3a2, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:10:21.265  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task started 
[TRACE] 2025-06-19 14:10:21.265  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:10:21.266  [HazelcastBaseNode-任务 19(6853a97351463b1f7959ad8e)-sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388)] AutoRecovery - Set auto-recovery enqueue: '6853a97351463b1f7959ad8e-6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 14:10:21.270  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:10:21.326  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 14:10:21.326  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:10:21.384  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 14:10:21.384  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:10:21.451  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2
[INFO ] 2025-06-19 14:10:21.451  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[TRACE] 2025-06-19 14:10:21.471  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:10:21.513  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388
[ERROR] 2025-06-19 14:10:21.561  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] PDK - ExternalJarManager [Copy encrypted jar file /Users/<USER>/IdeaProjects/tapdata_v3/dist/postgres-connector-v1.0-SNAPSHOT__6848fb35792b735df8979edf__.jar to /Users/<USER>/IdeaProjects/tapdata_v3/connectors/tap-running/postgres-connector-v1.0-SNAPSHOT__6848fb35792b735df8979edf___a1997fff-0f68-4f8e-a5fe-237370ca3f14.jar failed]
[INFO ] 2025-06-19 14:10:21.570  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] complete | Associate id: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535
[INFO ] 2025-06-19 14:10:21.592  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:10:21.770  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-8-thread-1] HazelcastPdkBaseNode - Create PDK connector on node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] complete | Associate id: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513
[INFO ] 2025-06-19 14:10:21.824  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:10:21.824  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:10:21.824  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:10:21.860  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:10:21.860  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - The table SampleTable has already exist. 
[INFO ] 2025-06-19 14:10:21.905  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:10:21.905  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:10:21.905  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:10:21.993  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:10:21.993  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - building CT table for table SampleTable 
[INFO ] 2025-06-19 14:10:22.092  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:10:29.365  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.365  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[INFO ] 2025-06-19 14:10:29.368  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-9-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[TRACE] 2025-06-19 14:10:29.368  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:10:29.369  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from table: SampleTable 
[TRACE] 2025-06-19 14:10:29.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SampleTable is going to be initial synced 
[TRACE] 2025-06-19 14:10:29.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[INFO ] 2025-06-19 14:10:29.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SampleTable has been completed batch read 
[TRACE] 2025-06-19 14:10:29.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:10:29.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:10:29.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:10:29.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:10:29.557  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting stream read, table list: [SampleTable], offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.557  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:10:29.762  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - opened cdc tables: [SampleTable] 
[WARN ] 2025-06-19 14:10:29.897  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6853a9d50c793fc2478cc85d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[04a3b729-701a-4cf5-93ec-d22f55ff7388], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-06-19 14:10:29.897  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after table "SampleTable" initial sync finished, cost: 17 ms 
[INFO ] 2025-06-19 14:10:29.897  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:10:30.384  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:10:30.384  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SampleTable], data change syncing 
[INFO ] 2025-06-19 14:10:47.026  [Thread-websocket-handle-message--2-thread-23] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}
[INFO ] 2025-06-19 14:10:47.026  [Thread-websocket-handle-message--2-thread-23] TapdataTaskScheduler - Send stop task operation: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:10:47.026  [Thread-websocket-handle-message--2-thread-23] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:10:47.027  [Stop-Task-Operation-Handler-6853a97351463b1f7959ad8e] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a97351463b1f7959ad8e'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 14:10:47.102  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 14:10:47.305  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync completed 
[WARN ] 2025-06-19 14:10:48.864  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[TRACE] 2025-06-19 14:10:50.111  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.111  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.112  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.112  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:10:50.113  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:10:50.113  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:10:50.121  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.122  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:10:50.122  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 9 ms 
[WARN ] 2025-06-19 14:10:53.868  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:10:54.296  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:10:58.874  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:10:58.878  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:10:58.878  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:10:59.884  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a97351463b1f7959ad8e'
[TRACE] 2025-06-19 14:10:59.884  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@38959f83 
[INFO ] 2025-06-19 14:10:59.886  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:10:59.886  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[TRACE] 2025-06-19 14:10:59.887  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@405e29c3 
[TRACE] 2025-06-19 14:10:59.887  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[INFO ] 2025-06-19 14:11:00.018  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 107 ms, stop join checker isDone: true]
[INFO ] 2025-06-19 14:11:00.021  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 19] status
[TRACE] 2025-06-19 14:11:00.021  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:11:00.021  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:11:00.021  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task stopped. 
[INFO ] 2025-06-19 14:11:00.065  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[TRACE] 2025-06-19 14:11:00.065  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:11:00.068  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[INFO ] 2025-06-19 14:11:08.375  [Thread-websocket-handle-message--2-thread-2] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:11:08.379  [Thread-websocket-handle-message--2-thread-2] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 14:11:08.473  [Thread-websocket-handle-message--2-thread-2] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:11:08.482  [Thread-websocket-handle-message--2-thread-2] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 14:11:08.696  [Thread-websocket-handle-message--2-thread-2] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=reset, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:13:12.762  [Thread-websocket-handle-message--2-thread-16] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-2085252417', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-2085252417', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:12.769  [Thread-websocket-handle-message--2-thread-16] ExternalStorageUtil - Init IMap store config succeed, name: -2085252417
[INFO ] 2025-06-19 14:13:12.863  [Thread-websocket-handle-message--2-thread-16] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1177672942', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1177672942', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:12.864  [Thread-websocket-handle-message--2-thread-16] ExternalStorageUtil - Init IMap store config succeed, name: 1177672942
[INFO ] 2025-06-19 14:13:12.960  [Thread-websocket-handle-message--2-thread-16] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=reset, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:13:15.489  [Thread-websocket-handle-message--2-thread-17] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}
[INFO ] 2025-06-19 14:13:15.489  [Thread-websocket-handle-message--2-thread-17] TapdataTaskScheduler - Send start task operation: 任务 18[6853a85b51463b1f7959ad32]
[INFO ] 2025-06-19 14:13:15.489  [Thread-websocket-handle-message--2-thread-17] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:13:15.489  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] ObsLoggerFactory - Clear mark with start task, task id: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:13:15.489  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 18, task id 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:13:15.525  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:13:15.526  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task initialization... 
[INFO ] 2025-06-19 14:13:15.533  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] AutoRecovery - Init auto-recovery instance '6853a85b51463b1f7959ad32'
[INFO ] 2025-06-19 14:13:15.534  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - start
[TRACE] 2025-06-19 14:13:15.580  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:13:15.581  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Loading table structure completed 
[INFO ] 2025-06-19 14:13:15.593  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:13:15.652  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:13:15.652  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:13:15.657  [hz.zed_flow_engine.cached.thread-12] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f358-c100-0001 based on submit request
[INFO ] 2025-06-19 14:13:15.658  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@11fd5e6a, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@704a5e4f, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853aa7851463b1f7959adec, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750313595472, stopedDate=null, needFilterEventData=null, testTaskId=6853a85b51463b1f7959ad30, transformTaskId=6853a85b51463b1f7959ad31, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:13:15.659  [hz.zed_flow_engine.cached.thread-12] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c101-0001
[INFO ] 2025-06-19 14:13:15.659  [hz.zed_flow_engine.cached.thread-12] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c101-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" [localParallelism=1];
	"sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [localParallelism=1];
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" -> "sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:13:15.664  [hz.zed_flow_engine.cached.thread-12] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f358-c100-0001, jobName='任务 18-6853a85b51463b1f7959ad32', executionId=0db5-f358-c101-0001 initialized
[INFO ] 2025-06-19 14:13:15.664  [hz.zed_flow_engine.cached.thread-12] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c101-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:13:15.664  [hz.zed_flow_engine.jet.blocking.thread-4] ExternalStorageUtil - Node sqlserver - ag2(id: 50db2b82-8d28-43d0-af3e-7cd9115522fb, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:13:15.664  [hz.zed_flow_engine.jet.blocking.thread-5] ExternalStorageUtil - Node sqlserver_ad(id: 55a9af1d-9bf0-4688-a23f-506667cc40db, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:13:15.668  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task started 
[TRACE] 2025-06-19 14:13:15.668  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[INFO ] 2025-06-19 14:13:15.668  [HazelcastBaseNode-任务 18(6853a85b51463b1f7959ad32)-sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb)] AutoRecovery - Set auto-recovery enqueue: '6853a85b51463b1f7959ad32-6853a85b51463b1f7959ad32'
[TRACE] 2025-06-19 14:13:15.668  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:13:15.668  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:15.668  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:15.673  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-10-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1177672942', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1177672942', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:15.730  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-10-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1177672942
[INFO ] 2025-06-19 14:13:15.731  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-2085252417', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-2085252417', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:15.790  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -2085252417
[INFO ] 2025-06-19 14:13:15.791  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-10-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:15.851  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-10-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db
[INFO ] 2025-06-19 14:13:15.851  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:15.914  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb
[INFO ] 2025-06-19 14:13:15.934  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-10-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] complete | Associate id: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313595898
[INFO ] 2025-06-19 14:13:15.965  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] complete | Associate id: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313595930
[INFO ] 2025-06-19 14:13:16.028  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#682169985b36c31a1fe9e6c9, target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:13:16.220  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:13:16.220  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:13:16.220  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:13:16.288  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[INFO ] 2025-06-19 14:13:16.288  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:13:16.288  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:13:16.288  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:13:16.288  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-19 14:13:16.417  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[WARN ] 2025-06-19 14:13:16.417  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:13:16.417  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[INFO ] 2025-06-19 14:13:16.544  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:16.593  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-11-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[INFO ] 2025-06-19 14:13:16.594  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:13:16.594  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:13:16.594  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:13:16.594  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:13:16.798  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:13:18.729  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:13:18.729  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:13:18.729  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:13:18.729  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:13:18.730  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:13:18.730  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:18.730  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:13:18.762  [Thread-websocket-handle-message--2-thread-18] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"6b9a3d58-9f1b-47c5-a95f-6e8a6062a589","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:13:18.770  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:13:18.796  [Thread-websocket-handle-message--2-thread-19] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"6b9a3d58-9f1b-47c5-a95f-6e8a6062a589","type":"testConnection"}
[INFO ] 2025-06-19 14:13:18.797  [Thread-76] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[WARN ] 2025-06-19 14:13:18.847  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:13:18.850  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:13:18.850  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:13:19.133  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:13:19.134  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:13:19.134  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:13:22.845  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:13:22.845  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:13:41.727  [Thread-websocket-handle-message--2-thread-22] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}
[INFO ] 2025-06-19 14:13:41.727  [Thread-websocket-handle-message--2-thread-22] TapdataTaskScheduler - Send stop task operation: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:13:41.727  [Thread-websocket-handle-message--2-thread-22] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:13:41.728  [Stop-Task-Operation-Handler-6853a85b51463b1f7959ad32] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a85b51463b1f7959ad32'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 14:13:41.857  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:13:42.469  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313595930 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313595930 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3005 ms 
[TRACE] 2025-06-19 14:13:44.806  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:13:44.812  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313595898 
[TRACE] 2025-06-19 14:13:44.812  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313595898 
[TRACE] 2025-06-19 14:13:44.812  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:13:44.812  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:13:44.812  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 5 ms 
[WARN ] 2025-06-19 14:13:45.186  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:13:45.210  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:13:50.193  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:13:50.195  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:13:50.292  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:13:51.199  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a85b51463b1f7959ad32'
[TRACE] 2025-06-19 14:13:51.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@54db08af 
[INFO ] 2025-06-19 14:13:51.199  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:13:51.199  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:13:51.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5421282f 
[INFO ] 2025-06-19 14:13:51.313  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 101 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 14:13:51.316  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[TRACE] 2025-06-19 14:13:51.316  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:13:51.316  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:13:51.316  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 18] status
[INFO ] 2025-06-19 14:13:51.316  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task stopped. 
[INFO ] 2025-06-19 14:13:51.363  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[TRACE] 2025-06-19 14:13:51.363  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[TRACE] 2025-06-19 14:13:51.363  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[INFO ] 2025-06-19 14:13:52.647  [Thread-websocket-handle-message--2-thread-24] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}
[INFO ] 2025-06-19 14:13:52.647  [Thread-websocket-handle-message--2-thread-24] TapdataTaskScheduler - Send start task operation: 任务 19[6853a97351463b1f7959ad8e]
[INFO ] 2025-06-19 14:13:52.647  [Thread-websocket-handle-message--2-thread-24] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:13:52.647  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ObsLoggerFactory - Clear mark with start task, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:13:52.647  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 19, task id 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:13:52.675  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:13:52.677  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task initialization... 
[INFO ] 2025-06-19 14:13:52.683  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] AutoRecovery - Init auto-recovery instance '6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 14:13:52.684  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - start
[TRACE] 2025-06-19 14:13:52.732  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:13:52.732  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Loading table structure completed 
[INFO ] 2025-06-19 14:13:52.748  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:13:52.818  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:13:52.818  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:13:52.823  [hz.zed_flow_engine.cached.thread-9] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f358-c102-0001 based on submit request
[INFO ] 2025-06-19 14:13:52.823  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@60098594, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@2d16004a, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853a9fc51463b1f7959ade2, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750313632629, stopedDate=null, needFilterEventData=null, testTaskId=6853a97351463b1f7959ad8c, transformTaskId=6853a97351463b1f7959ad8d, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:13:52.825  [hz.zed_flow_engine.cached.thread-9] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f358-c103-0001
[INFO ] 2025-06-19 14:13:52.825  [hz.zed_flow_engine.cached.thread-9] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f358-c103-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" [localParallelism=1];
	"local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [localParallelism=1];
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" -> "local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:13:52.831  [hz.zed_flow_engine.cached.thread-9] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f358-c102-0001, jobName='任务 19-6853a97351463b1f7959ad8e', executionId=0db5-f358-c103-0001 initialized
[INFO ] 2025-06-19 14:13:52.831  [hz.zed_flow_engine.cached.thread-9] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-f358-c103-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:13:52.831  [Target-Process-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]] ExternalStorageUtil - Node local_pg(id: db6ac1f3-d782-46d1-9065-615be84ff3a2, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:13:52.831  [Source-Complete-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]] ExternalStorageUtil - Node sqlserver_ad(id: 04a3b729-701a-4cf5-93ec-d22f55ff7388, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:13:52.834  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task started 
[TRACE] 2025-06-19 14:13:52.834  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:52.835  [HazelcastBaseNode-任务 19(6853a97351463b1f7959ad8e)-sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388)] AutoRecovery - Set auto-recovery enqueue: '6853a97351463b1f7959ad8e-6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 14:13:52.838  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:52.898  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 14:13:52.899  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-12-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:52.958  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-12-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 14:13:52.959  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:13:53.019  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388
[INFO ] 2025-06-19 14:13:53.020  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-12-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[TRACE] 2025-06-19 14:13:53.040  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:53.082  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-12-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2
[INFO ] 2025-06-19 14:13:53.118  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] complete | Associate id: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082
[INFO ] 2025-06-19 14:13:53.178  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:13:53.214  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-12-thread-1] HazelcastPdkBaseNode - Create PDK connector on node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] complete | Associate id: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095
[INFO ] 2025-06-19 14:13:53.226  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:13:53.226  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:13:53.226  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:13:53.239  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:13:53.418  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:13:53.419  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:13:53.419  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:13:53.419  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:13:53.419  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:13:53.546  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - building CT table for table SourceOfRegion 
[INFO ] 2025-06-19 14:13:53.681  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:13:53.845  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:53.913  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-13-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[INFO ] 2025-06-19 14:13:53.914  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:13:53.914  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:13:53.914  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:13:53.914  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:13:54.086  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:13:54.087  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:13:54.097  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:13:54.097  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:13:54.109  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:13:54.109  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:13:54.118  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:13:54.118  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:13:54.127  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:13:54.127  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:13:54.138  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:13:54.138  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:13:54.339  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:54.524  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:13:54.703  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:13:54.913  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:13:54.914  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:13:54.928  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:13:54.928  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-06-19 14:15:36.389  [Thread-websocket-handle-message--2-thread-12] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}
[INFO ] 2025-06-19 14:15:36.390  [Thread-websocket-handle-message--2-thread-12] TapdataTaskScheduler - Send stop task operation: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:15:36.390  [Thread-websocket-handle-message--2-thread-12] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:15:36.391  [Stop-Task-Operation-Handler-6853a97351463b1f7959ad8e] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a97351463b1f7959ad8e'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 14:15:36.397  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[WARN ] 2025-06-19 14:15:36.441  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[TRACE] 2025-06-19 14:15:37.207  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 14:15:39.406  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.407  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:15:39.407  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:15:39.407  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:15:39.420  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.421  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:15:39.421  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[WARN ] 2025-06-19 14:15:41.445  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:15:41.836  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:15:46.453  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:15:46.455  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:15:46.503  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:15:47.461  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a97351463b1f7959ad8e'
[TRACE] 2025-06-19 14:15:47.462  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@12e5e795 
[INFO ] 2025-06-19 14:15:47.462  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:15:47.462  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:15:47.462  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23e9bf10 
[INFO ] 2025-06-19 14:15:47.582  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 104 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 14:15:47.584  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 14:15:47.584  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stopped task aspect(s) 
[INFO ] 2025-06-19 14:15:47.585  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 19] status
[TRACE] 2025-06-19 14:15:47.585  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:15:47.585  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task stopped. 
[INFO ] 2025-06-19 14:15:47.626  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[TRACE] 2025-06-19 14:15:47.626  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:15:47.629  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[INFO ] 2025-06-19 14:16:51.857  [Thread-websocket-handle-message--2-thread-20] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"6b9a3d58-9f1b-47c5-a95f-6e8a6062a589","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:16:51.866  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:16:51.887  [Thread-websocket-handle-message--2-thread-21] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"6b9a3d58-9f1b-47c5-a95f-6e8a6062a589","type":"testConnection"}
[INFO ] 2025-06-19 14:16:51.887  [Thread-80] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:16:55.474  [Thread-websocket-handle-message--2-thread-23] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"6b9a3d58-9f1b-47c5-a95f-6e8a6062a589","type":"testConnection"}
[INFO ] 2025-06-19 14:16:55.475  [Thread-81] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:16:57.219  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Starting load schema fields, connection name: sqlserver_ad
[INFO ] 2025-06-19 14:16:58.530  [LOAD-SCHEMA-FIELDS-[sqlserver_ad]] LoadSchemaRunner - Finished load schema fields, connection name: sqlserver_ad, progress: 3/3
[INFO ] 2025-06-19 14:16:59.828  [Thread-websocket-handle-message--2-thread-24] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-2085252417', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-2085252417', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:16:59.833  [Thread-websocket-handle-message--2-thread-24] ExternalStorageUtil - Init IMap store config succeed, name: -2085252417
[INFO ] 2025-06-19 14:16:59.921  [Thread-websocket-handle-message--2-thread-24] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1177672942', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1177672942', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:16:59.922  [Thread-websocket-handle-message--2-thread-24] ExternalStorageUtil - Init IMap store config succeed, name: 1177672942
[INFO ] 2025-06-19 14:17:00.021  [Thread-websocket-handle-message--2-thread-24] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=reset, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:17:01.428  [Thread-websocket-handle-message--2-thread-1] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}
[INFO ] 2025-06-19 14:17:01.432  [Thread-websocket-handle-message--2-thread-1] TapdataTaskScheduler - Send start task operation: 任务 18[6853a85b51463b1f7959ad32]
[INFO ] 2025-06-19 14:17:01.432  [Thread-websocket-handle-message--2-thread-1] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:17:01.432  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] ObsLoggerFactory - Clear mark with start task, task id: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:17:01.432  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 18, task id 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:17:01.460  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:17:01.463  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task initialization... 
[TRACE] 2025-06-19 14:17:01.465  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Start task milestones: 6853a85b51463b1f7959ad32(任务 18) 
[INFO ] 2025-06-19 14:17:01.465  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] AutoRecovery - Init auto-recovery instance '6853a85b51463b1f7959ad32'
[INFO ] 2025-06-19 14:17:01.466  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - start
[INFO ] 2025-06-19 14:17:01.528  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:17:01.558  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Loading table structure completed 
[TRACE] 2025-06-19 14:17:01.558  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2025-06-19 14:17:01.595  [hz.zed_flow_engine.cached.thread-5] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-f358-c104-0001 based on submit request
[TRACE] 2025-06-19 14:17:01.595  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:17:01.595  [Start-Task-Operation-Handler-任务 18[6853a85b51463b1f7959ad32]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@7fa4db37, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@7273795f, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853ab5b51463b1f7959ae7f, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750313821418, stopedDate=null, needFilterEventData=null, testTaskId=6853a85b51463b1f7959ad30, transformTaskId=6853a85b51463b1f7959ad31, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=0, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:17:01.595  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task started 
[INFO ] 2025-06-19 14:17:01.596  [hz.zed_flow_engine.cached.thread-5] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c105-0001
[INFO ] 2025-06-19 14:17:01.596  [hz.zed_flow_engine.cached.thread-5] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c105-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" [localParallelism=1];
	"sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [localParallelism=1];
	"sqlserver - ag2-50db2b82-8d28-43d0-af3e-7cd9115522fb" -> "sqlserver_ad-55a9af1d-9bf0-4688-a23f-506667cc40db" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:17:01.601  [hz.zed_flow_engine.cached.thread-5] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-f358-c104-0001, jobName='任务 18-6853a85b51463b1f7959ad32', executionId=0db5-f358-c105-0001 initialized
[INFO ] 2025-06-19 14:17:01.601  [hz.zed_flow_engine.cached.thread-12] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 18-6853a85b51463b1f7959ad32', execution 0db5-f358-c105-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:17:01.602  [hz.zed_flow_engine.jet.blocking.thread-6] ExternalStorageUtil - Node sqlserver - ag2(id: 50db2b82-8d28-43d0-af3e-7cd9115522fb, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:17:01.602  [hz.zed_flow_engine.jet.blocking.thread-7] ExternalStorageUtil - Node sqlserver_ad(id: 55a9af1d-9bf0-4688-a23f-506667cc40db, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:17:01.605  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:17:01.605  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] start preload schema,table counts: 1 
[INFO ] 2025-06-19 14:17:01.605  [HazelcastBaseNode-任务 18(6853a85b51463b1f7959ad32)-sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb)] AutoRecovery - Set auto-recovery enqueue: '6853a85b51463b1f7959ad32-6853a85b51463b1f7959ad32'
[TRACE] 2025-06-19 14:17:01.605  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:17:01.605  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:17:01.609  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-2085252417', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-2085252417', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:17:01.671  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -2085252417
[INFO ] 2025-06-19 14:17:01.672  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-14-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1177672942', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1177672942', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:17:01.741  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-14-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1177672942
[INFO ] 2025-06-19 14:17:01.741  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:17:01.803  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_50db2b82-8d28-43d0-af3e-7cd9115522fb
[INFO ] 2025-06-19 14:17:01.803  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-14-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:17:01.859  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-14-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_55a9af1d-9bf0-4688-a23f-506667cc40db
[INFO ] 2025-06-19 14:17:01.893  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] complete | Associate id: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313821859
[INFO ] 2025-06-19 14:17:01.924  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db]@task-任务 18-14-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] complete | Associate id: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313821873
[INFO ] 2025-06-19 14:17:01.964  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#682169985b36c31a1fe9e6c9, target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:17:02.202  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Sink connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:17:02.202  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node(sqlserver_ad) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:17:02.202  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-19 14:17:02.207  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source connector(sqlserver - ag2) initialization completed 
[TRACE] 2025-06-19 14:17:02.207  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" read batch size: 100 
[TRACE] 2025-06-19 14:17:02.208  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Source node "sqlserver - ag2" event queue capacity: 200 
[TRACE] 2025-06-19 14:17:02.208  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:17:02.280  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Apply table structure to target database 
[WARN ] 2025-06-19 14:17:02.280  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:17:02.280  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[TRACE] 2025-06-19 14:17:02.399  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:17:02.399  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Use existing stream offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:17:02.447  [HazelcastSourcePdkBaseNode_Source-Runner-6853a85b51463b1f7959ad32-sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb]-15-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[INFO ] 2025-06-19 14:17:02.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:17:02.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync started 
[INFO ] 2025-06-19 14:17:02.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:17:02.447  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:17:02.651  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Query snapshot row size completed: sqlserver - ag2(50db2b82-8d28-43d0-af3e-7cd9115522fb) 
[INFO ] 2025-06-19 14:17:04.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:17:04.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[INFO ] 2025-06-19 14:17:04.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Batch read completed. 
[TRACE] 2025-06-19 14:17:04.556  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync starting... 
[TRACE] 2025-06-19 14:17:04.557  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Initial sync completed 
[TRACE] 2025-06-19 14:17:04.557  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000004A0001C1B80001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:17:04.681  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Starting incremental sync using database log parser 
[WARN ] 2025-06-19 14:17:04.681  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Failed to get cdc tables for dbo failed 
[INFO ] 2025-06-19 14:17:04.681  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - opened cdc tables: [] 
[INFO ] 2025-06-19 14:17:04.681  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - building CT table for table SourceOfRegion 
[WARN ] 2025-06-19 14:17:04.955  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - dbcc traceon 1448 failed: User 'tapdata' does not have permission to run DBCC TRACEON. 
[INFO ] 2025-06-19 14:17:04.955  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:17:04.956  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-06-19 14:17:05.361  [Thread-websocket-handle-message--2-thread-3] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:17:05.369  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:17:05.395  [Thread-websocket-handle-message--2-thread-4] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:17:05.395  [Thread-83] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[TRACE] 2025-06-19 14:17:08.345  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:17:08.346  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:22:12.695  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:22:12.695  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#682169985b36c31a1fe9e6c9, target#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:22:13.500  [Thread-websocket-handle-message--2-thread-11] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}
[INFO ] 2025-06-19 14:22:13.500  [Thread-websocket-handle-message--2-thread-11] TapdataTaskScheduler - Send stop task operation: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 14:22:13.500  [Thread-websocket-handle-message--2-thread-11] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a85b51463b1f7959ad32, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:22:13.501  [Stop-Task-Operation-Handler-6853a85b51463b1f7959ad32] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a85b51463b1f7959ad32'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 14:22:13.662  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] running status set to false 
[TRACE] 2025-06-19 14:22:14.113  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Incremental sync completed 
[TRACE] 2025-06-19 14:22:16.514  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node stopped: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313821859 
[TRACE] 2025-06-19 14:22:16.515  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - PDK connector node released: HazelcastSourcePdkDataNode_50db2b82-8d28-43d0-af3e-7cd9115522fb_1750313821859 
[TRACE] 2025-06-19 14:22:16.515  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] schema data cleaned 
[TRACE] 2025-06-19 14:22:16.515  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] monitor closed 
[TRACE] 2025-06-19 14:22:16.516  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver - ag2] - Node sqlserver - ag2[50db2b82-8d28-43d0-af3e-7cd9115522fb] close complete, cost 3011 ms 
[TRACE] 2025-06-19 14:22:16.516  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] running status set to false 
[TRACE] 2025-06-19 14:22:16.528  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node stopped: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313821873 
[TRACE] 2025-06-19 14:22:16.528  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - PDK connector node released: HazelcastTargetPdkDataNode_55a9af1d-9bf0-4688-a23f-506667cc40db_1750313821873 
[TRACE] 2025-06-19 14:22:16.528  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] schema data cleaned 
[TRACE] 2025-06-19 14:22:16.528  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] monitor closed 
[TRACE] 2025-06-19 14:22:16.732  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18][sqlserver_ad] - Node sqlserver_ad[55a9af1d-9bf0-4688-a23f-506667cc40db] close complete, cost 12 ms 
[WARN ] 2025-06-19 14:22:17.892  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:22:17.994  [6853a85b51463b1f7959ad32-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 14:22:22.898  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 14:22:22.900  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 14:22:23.019  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 14:22:23.905  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a85b51463b1f7959ad32'
[TRACE] 2025-06-19 14:22:23.905  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@78e459a6 
[INFO ] 2025-06-19 14:22:23.905  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:22:23.905  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[TRACE] 2025-06-19 14:22:23.905  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@49562205 
[INFO ] 2025-06-19 14:22:24.022  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 103 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 14:22:24.024  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stop task milestones: 6853a85b51463b1f7959ad32(任务 18)  
[TRACE] 2025-06-19 14:22:24.024  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:22:24.024  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:22:24.024  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 18] status
[INFO ] 2025-06-19 14:22:24.025  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Task stopped. 
[TRACE] 2025-06-19 14:22:24.072  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Remove memory task client succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[INFO ] 2025-06-19 14:22:24.072  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a85b51463b1f7959ad32
[TRACE] 2025-06-19 14:22:24.072  [Read_File_Appender_TAILER_Thread] job-file-log-6853a85b51463b1f7959ad32 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[6853a85b51463b1f7959ad32] 
[INFO ] 2025-06-19 14:28:12.715  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:28:12.715  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:33:12.728  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:33:12.728  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:38:12.741  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:38:12.741  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:43:12.752  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:43:12.752  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:45:45.143  [Thread-websocket-handle-message--2-thread-8] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:45:45.154  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:45:45.179  [Thread-websocket-handle-message--2-thread-9] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:45:45.180  [Thread-105] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:46:25.233  [Thread-websocket-handle-message--2-thread-14] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:46:25.240  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:46:25.256  [Thread-websocket-handle-message--2-thread-15] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:46:25.256  [Thread-106] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:46:37.276  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:37.283  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 14:46:37.376  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:37.379  [Thread-websocket-handle-message--2-thread-17] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 14:46:37.594  [Thread-websocket-handle-message--2-thread-17] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=reset, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:46:39.031  [Thread-websocket-handle-message--2-thread-18] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}
[INFO ] 2025-06-19 14:46:39.032  [Thread-websocket-handle-message--2-thread-18] TapdataTaskScheduler - Send start task operation: 任务 19[6853a97351463b1f7959ad8e]
[INFO ] 2025-06-19 14:46:39.032  [Thread-websocket-handle-message--2-thread-18] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 14:46:39.032  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ObsLoggerFactory - Clear mark with start task, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:46:39.032  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 19, task id 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 14:46:39.062  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 14:46:39.066  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:46:39.066  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:46:39.068  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] AutoRecovery - Init auto-recovery instance '6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 14:46:39.069  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - start
[INFO ] 2025-06-19 14:46:39.131  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:46:39.163  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:46:39.166  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2025-06-19 14:46:39.213  [hz.zed_flow_engine.cached.thread-7] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db5-fafd-55c0-0001 based on submit request
[TRACE] 2025-06-19 14:46:39.213  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:46:39.213  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task started 
[INFO ] 2025-06-19 14:46:39.213  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@7b0317a6, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@a1b7867, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853b24d51463b1f7959aeba, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=null, snapshotDoneAt=null, scheduleDate=1750315599014, stopedDate=null, needFilterEventData=null, testTaskId=6853a97351463b1f7959ad8c, transformTaskId=6853a97351463b1f7959ad8d, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=null, shareCdcStop=null, shareCdcStopMessage=null, delayTime=-861, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 14:46:39.215  [hz.zed_flow_engine.cached.thread-7] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-fafd-55c1-0001
[INFO ] 2025-06-19 14:46:39.215  [hz.zed_flow_engine.cached.thread-7] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-fafd-55c1-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" [localParallelism=1];
	"local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [localParallelism=1];
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" -> "local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 14:46:39.220  [hz.zed_flow_engine.cached.thread-7] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db5-fafd-55c0-0001, jobName='任务 19-6853a97351463b1f7959ad8e', executionId=0db5-fafd-55c1-0001 initialized
[INFO ] 2025-06-19 14:46:39.220  [hz.zed_flow_engine.cached.thread-7] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 19-6853a97351463b1f7959ad8e', execution 0db5-fafd-55c1-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 14:46:39.221  [hz.zed_flow_engine.jet.blocking.thread-8] ExternalStorageUtil - Node sqlserver_ad(id: 04a3b729-701a-4cf5-93ec-d22f55ff7388, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 14:46:39.221  [hz.zed_flow_engine.jet.blocking.thread-9] ExternalStorageUtil - Node local_pg(id: db6ac1f3-d782-46d1-9065-615be84ff3a2, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 14:46:39.224  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[INFO ] 2025-06-19 14:46:39.224  [HazelcastBaseNode-任务 19(6853a97351463b1f7959ad8e)-sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388)] AutoRecovery - Set auto-recovery enqueue: '6853a97351463b1f7959ad8e-6853a97351463b1f7959ad8e'
[TRACE] 2025-06-19 14:46:39.224  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:46:39.224  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:46:39.224  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:46:39.228  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-16-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:39.292  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-16-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 14:46:39.293  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:39.354  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 14:46:39.354  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-16-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:39.424  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-16-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_db6ac1f3-d782-46d1-9065-615be84ff3a2
[INFO ] 2025-06-19 14:46:39.425  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 14:46:39.501  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: PdkStateMap_04a3b729-701a-4cf5-93ec-d22f55ff7388
[INFO ] 2025-06-19 14:46:39.553  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] complete | Associate id: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518
[INFO ] 2025-06-19 14:46:39.562  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:46:39.629  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-16-thread-1] HazelcastPdkBaseNode - Create PDK connector on node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] complete | Associate id: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501
[INFO ] 2025-06-19 14:46:39.636  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:46:39.636  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:46:39.636  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:46:39.646  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:46:39.851  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:46:39.895  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:46:39.896  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:46:39.896  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:46:39.896  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:46:40.063  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:46:40.127  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.198  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-2] HazelcastSourcePdkBaseNode - Start to asynchronously count the size of rows for the source table(s)
[INFO ] 2025-06-19 14:46:40.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:46:40.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:46:40.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:46:40.199  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:46:40.278  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:46:40.364  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:46:40.364  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:46:40.373  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:46:40.373  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:46:40.380  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:46:40.380  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:46:40.388  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:46:40.388  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:46:40.396  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:46:40.396  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:46:40.400  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:46:40.400  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:46:40.764  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:46:40.764  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:46:40.764  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:46:40.764  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:46:40.764  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:46:40.765  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.765  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:46:40.965  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:46:41.165  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:46:41.166  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:46:41.168  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:46:41.370  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-06-19 14:48:09.140  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-17-thread-2] PDK - HazelcastPdkBaseNode [methodEnd - SOURCE_STREAM_READ | message - (The client connection was terminated by the sqlserver server)]
[WARN ] 2025-06-19 14:48:09.148  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 14:48:09.234  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 14:48:09, next retry time 2025-06-19 14:49:09 
[INFO ] 2025-06-19 14:48:56.491  [Thread-websocket-handle-message--2-thread-9] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:48:56.506  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:48:56.570  [Thread-websocket-handle-message--2-thread-10] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:48:56.571  [Thread-109] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:49:09.722  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 14:49:09.789  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:49:09.792  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 14:49:09.792  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.625000 
[TRACE] 2025-06-19 14:49:09.995  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-06-19 14:50:24.774  [Thread-websocket-handle-message--2-thread-19] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:50:24.784  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:50:24.804  [Thread-websocket-handle-message--2-thread-20] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:50:24.804  [Thread-111] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:50:36.234  [Thread-websocket-handle-message--2-thread-23] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:50:36.245  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:50:36.272  [Thread-websocket-handle-message--2-thread-24] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:50:36.272  [Thread-112] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:50:52.514  [Thread-websocket-handle-message--2-thread-2] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:50:52.522  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:50:52.543  [Thread-websocket-handle-message--2-thread-3] TestConnectionHandler - Test connection 'null', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"sqlserver","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:50:52.543  [Thread-113] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:51:02.132  [Thread-websocket-handle-message--2-thread-5] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 14:51:02.139  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 14:51:02.162  [Thread-websocket-handle-message--2-thread-6] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 14:51:02.164  [Thread-114] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 14:52:12.774  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:52:12.774  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 14:57:12.784  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 14:57:12.784  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:02:12.799  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:02:12.799  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:04:25.672  [Remove-Task-Logger-Scheduler] ObsLoggerFactory - Remove task logger, task id: 6853a80551463b1f7959acc7
[INFO ] 2025-06-19 15:08:12.804  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:08:12.804  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:13:12.816  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:13:12.816  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:18:12.834  [EMS-3-thread-1] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:18:12.834  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:22:26.733  [Remove-Task-Logger-Scheduler] ObsLoggerFactory - Remove task logger, task id: 6853a85b51463b1f7959ad32
[INFO ] 2025-06-19 15:23:12.852  [EMS-3-thread-2] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:23:12.852  [EMS-3-thread-3] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[WARN ] 2025-06-19 15:29:08.123  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] MonitorInvocationsTask delayed 123201 ms
[INFO ] 2025-06-19 15:29:12.100  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] System clock apparently jumped from 2025-06-19 15:27:03.893 to 2025-06-19 15:29:12.099 since last heartbeat (+123206 ms)
[WARN ] 2025-06-19 15:29:12.100  [hz.zed_flow_engine.cached.thread-11] ClusterHeartbeatManager - [**********]:5701 [dev] [5.5.0] Resetting heartbeat timestamps because of huge system clock jump! Clock-Jump: 123206 ms, Heartbeat-Timeout: 60000 ms
[INFO ] 2025-06-19 15:29:16.083  [EMS-3-thread-3] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:29:16.084  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:29:16.796  [WebSocketClient-AsyncIO-2] ManagementWebsocketHandler - Web socket closed, session: ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030, status code: 1000, reason: null
[INFO ] 2025-06-19 15:29:22.291  [Management Websocket Health Check] ManagementWebsocketHandler - Connect to web socket server success, url ws://localhost:3000/ws/agent?agentId=zed_flow_engine&access_token=cfa3ed89cb0640d3bf0f1572687e78a08fd3e1353fdf4243b88402cec670127f&singletonTag=a25ed299-81df-4082-b03b-f315ed248030
[INFO ] 2025-06-19 15:29:26.803  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStartTask
[INFO ] 2025-06-19 15:29:26.803  [WebSocketClient-AsyncIO-2] TapdataTaskScheduler - Start schedule task: scheduleStopTask
[INFO ] 2025-06-19 15:29:32.401  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStartTask
[INFO ] 2025-06-19 15:29:32.404  [SimpleAsyncTaskExecutor-1] TapdataTaskScheduler - Stop schedule task: scheduleStopTask
[WARN ] 2025-06-19 15:29:47.136  [hz.zed_flow_engine.InvocationMonitorThread] InvocationMonitor - [**********]:5701 [dev] [5.5.0] BroadcastOperationControlTask delayed 123218 ms
[INFO ] 2025-06-19 15:30:02.868  [Thread-websocket-handle-message--2-thread-11] DownLoadConnectorHandler - downLoad connector, entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"downLoadConnector"}
[INFO ] 2025-06-19 15:30:02.917  [DOWNLOAD-CONNECTOR-sqlserver_ad] DownLoadConnectorHandler - Whether to start downloading the pdk file false
[INFO ] 2025-06-19 15:30:02.946  [Thread-websocket-handle-message--2-thread-12] TestConnectionHandler - Test connection '************:1433/ADTest/dbo', entity: {"connectionId":"6853a7f951463b1f7959acc0","connectionName":"sqlserver_ad","databaseType":"SQL Server","pdkHash":"666f77739883446032167ffc22c4ddcb9b4725cf6697a321734253facb858247","pdkType":"pdk","schemaVersion":"990e5756-7236-46ce-b64f-87abf946d0b2","type":"testConnection"}
[INFO ] 2025-06-19 15:30:02.946  [Thread-142] TestConnectionHandler - Starting validate connections name: sqlserver_ad.
[INFO ] 2025-06-19 15:30:14.674  [Thread-websocket-handle-message--2-thread-14] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}
[INFO ] 2025-06-19 15:30:14.674  [Thread-websocket-handle-message--2-thread-14] TapdataTaskScheduler - Send stop task operation: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:30:14.674  [Thread-websocket-handle-message--2-thread-14] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 15:30:14.675  [Stop-Task-Operation-Handler-6853a97351463b1f7959ad8e] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a97351463b1f7959ad8e'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 15:30:14.779  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[WARN ] 2025-06-19 15:30:14.977  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  COMPLETING [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[TRACE] 2025-06-19 15:30:14.982  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:30:17.786  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.786  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:30:17.786  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:30:17.799  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:30:17.799  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.799  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:30:18.005  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[WARN ] 2025-06-19 15:30:19.980  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 15:30:20.197  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - exit
[WARN ] 2025-06-19 15:30:24.984  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  FAILED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 15:30:24.987  [Internal Stop Task Scheduler[zed_flow_engine]] TaskInspect - TaskInspect release instance...
[TRACE] 2025-06-19 15:30:25.105  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2025-06-19 15:30:25.992  [Internal Stop Task Scheduler[zed_flow_engine]] AutoRecovery - Releasing auto-recovery instance '6853a97351463b1f7959ad8e'
[TRACE] 2025-06-19 15:30:25.993  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@65fd2f7c 
[TRACE] 2025-06-19 15:30:25.993  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@545248ab 
[INFO ] 2025-06-19 15:30:25.993  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:30:25.993  [EMS-3-thread-2] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:30:26.113  [Internal Stop Task Scheduler[zed_flow_engine]] PDK - TapCompletableFutureEx [Stop done, timeout: 15000 ms, cost time: 104 ms, stop join checker isDone: true]
[TRACE] 2025-06-19 15:30:26.114  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 15:30:26.115  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:30:26.115  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:30:26.115  [Internal Stop Task Scheduler[zed_flow_engine]] TapdataTaskScheduler - Call Task/stopped api to modify task [任务 19] status
[INFO ] 2025-06-19 15:30:26.168  [Internal Stop Task Scheduler[zed_flow_engine]] ObsLoggerFactory - Add mark with call remove task logger, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:30:26.168  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 15:30:26.169  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:30:26.169  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[INFO ] 2025-06-19 15:36:16.112  [EMS-3-thread-4] PDK - ProxySubscriptionManager [Start to sync subscribe ids after idle 300 seconds]
[INFO ] 2025-06-19 15:36:16.112  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:37:37.952  [Thread-websocket-handle-message--2-thread-11] BaseEventHandler - Start task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}
[INFO ] 2025-06-19 15:37:37.952  [Thread-websocket-handle-message--2-thread-11] TapdataTaskScheduler - Send start task operation: 任务 19[6853a97351463b1f7959ad8e]
[INFO ] 2025-06-19 15:37:37.952  [Thread-websocket-handle-message--2-thread-11] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=start, force=false}}, code='null'}.
[INFO ] 2025-06-19 15:37:37.952  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ObsLoggerFactory - Clear mark with start task, task id: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:37:37.952  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - The task to be scheduled is found, task name 任务 19, task id 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:37:37.984  [EMS-3-thread-4] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [processId_zed_flow_engine]]
[TRACE] 2025-06-19 15:37:37.985  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 15:37:37.988  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 15:37:37.991  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] AutoRecovery - Init auto-recovery instance '6853a97351463b1f7959ad8e'
[INFO ] 2025-06-19 15:37:37.993  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - start
[INFO ] 2025-06-19 15:37:38.060  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] ExternalStorageUtil - Task init external storage configs completed: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 15:37:38.096  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 15:37:38.096  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Node performs snapshot read asynchronously 
[INFO ] 2025-06-19 15:37:38.149  [hz.zed_flow_engine.cached.thread-18] JobCoordinationService - [**********]:5701 [dev] [5.5.0] Starting job 0db6-06a8-9400-0001 based on submit request
[TRACE] 2025-06-19 15:37:38.149  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:37:38.149  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19] - Task started 
[INFO ] 2025-06-19 15:37:38.149  [Start-Task-Operation-Handler-任务 19[6853a97351463b1f7959ad8e]] TapdataTaskScheduler - Handled task operation: StartTaskOperation{taskDto=TaskDto(dag=com.tapdata.tm.commons.dag.DAG@7c211ae3, shareCache=false, canOpenInspect=false, isAutoInspect=false, skipErrorEvent=com.tapdata.tm.commons.task.dto.TaskDto$SkipErrorEvent@15f8c751, creator=null, showInspectTips=false, inspectId=null, logSetting=null, fdmMain=null, tempDag=null, resetFlag=null, deleteFlag=null, version=null, taskRecordId=6853b24d51463b1f7959aeba, alarmSettings=[AlarmSettingVO(type=TASK, open=true, key=TASK_STATUS_ERROR, sort=1, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_FULL_COMPLETE, sort=3, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_START, sort=4, notify=[SYSTEM, EMAIL], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INCREMENT_DELAY, sort=6, notify=[SYSTEM], interval=300, unit=SECOND, params=null), AlarmSettingVO(type=TASK, open=true, key=TASK_INSPECT_DIFFERENCE, sort=1, notify=[SYSTEM], interval=300, unit=SECOND, params=null)], alarmRules=[AlarmRuleVO(key=TASK_INCREMENT_DELAY, point=60, equalsFlag=1, ms=60000)], emailReceivers=[<EMAIL>], resetTimes=null, currentEventTimestamp=1750316004637, snapshotDoneAt=1750315601165, scheduleDate=1750318657941, stopedDate=null, needFilterEventData=null, testTaskId=6853a97351463b1f7959ad8c, transformTaskId=6853a97351463b1f7959ad8d, stopRetryTimes=0, isSnapShotInterrupt=false, ldpType=null, ldpNewTables=null, functionRetryStatus=null, taskRetryStartTime=0, shareCdcStop=null, shareCdcStopMessage=null, delayTime=282190, isomorphism=null, dynamicAdjustMemoryUsage=false, dynamicAdjustMemoryThresholdByte=null, dynamicAdjustMemorySampleRate=null, doubleActive=false, enableSyncMetricCollector=null, errorEvents=null, agentGroupInfo=null, oldVersionTimezone=false, timeDifference=null, previewRows=null, retryIntervalSecond=null, maxRetryTimeMinute=null, preview=false, testUsingPreview=false, env=null, taskInfo=null)} TaskOperation{, opType=START}
[INFO ] 2025-06-19 15:37:38.151  [hz.zed_flow_engine.cached.thread-18] MasterJobContext - [**********]:5701 [dev] [5.5.0] Didn't find any snapshot to restore for job '任务 19-6853a97351463b1f7959ad8e', execution 0db6-06a8-9401-0001
[INFO ] 2025-06-19 15:37:38.151  [hz.zed_flow_engine.cached.thread-18] MasterJobContext - [**********]:5701 [dev] [5.5.0] Start executing job '任务 19-6853a97351463b1f7959ad8e', execution 0db6-06a8-9401-0001, execution graph in DOT format:
digraph DAG {
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" [localParallelism=1];
	"local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [localParallelism=1];
	"sqlserver_ad-04a3b729-701a-4cf5-93ec-d22f55ff7388" -> "local_pg-db6ac1f3-d782-46d1-9065-615be84ff3a2" [queueSize=128];
}
HINT: You can use graphviz or http://viz-js.com to visualize the printed graph.
[INFO ] 2025-06-19 15:37:38.156  [hz.zed_flow_engine.cached.thread-18] JobExecutionService - [**********]:5701 [dev] [5.5.0] Execution plan for jobId=0db6-06a8-9400-0001, jobName='任务 19-6853a97351463b1f7959ad8e', executionId=0db6-06a8-9401-0001 initialized
[INFO ] 2025-06-19 15:37:38.156  [hz.zed_flow_engine.cached.thread-18] JobExecutionService - [**********]:5701 [dev] [5.5.0] Start execution of job '任务 19-6853a97351463b1f7959ad8e', execution 0db6-06a8-9401-0001 from coordinator [**********]:5701
[INFO ] 2025-06-19 15:37:38.156  [hz.zed_flow_engine.jet.blocking.thread-10] ExternalStorageUtil - Node sqlserver_ad(id: 04a3b729-701a-4cf5-93ec-d22f55ff7388, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[INFO ] 2025-06-19 15:37:38.156  [hz.zed_flow_engine.jet.blocking.thread-11] ExternalStorageUtil - Node local_pg(id: db6ac1f3-d782-46d1-9065-615be84ff3a2, type: DatabaseNode) use external storage config: Tapdata MongoDB External Storage
[TRACE] 2025-06-19 15:37:38.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[INFO ] 2025-06-19 15:37:38.164  [HazelcastBaseNode-任务 19(6853a97351463b1f7959ad8e)-sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388)] AutoRecovery - Set auto-recovery enqueue: '6853a97351463b1f7959ad8e-6853a97351463b1f7959ad8e'
[TRACE] 2025-06-19 15:37:38.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:37:38.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:37:38.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:37:38.168  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-19-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='-705764950', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_-705764950', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 15:37:38.173  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-19-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: -705764950
[INFO ] 2025-06-19 15:37:38.173  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-18-thread-1] ExternalStorageUtil - Added hazelcast persistence config: PersistenceMongoDBConfig[constructType=IMAP, name='1464824568', storageMode=MongoDB, inMemSize=1000, uri='mongodb://localhost:27017/tapdata_355', database='tapdata_355', collection='ExternalStorage_1464824568', exclusiveCollection=true, ssl=false, sslCA='null', sslKey='null', sslPass='null', sslValidate=false, checkServerIdentity=false]
[INFO ] 2025-06-19 15:37:38.174  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-18-thread-1] ExternalStorageUtil - Init IMap store config succeed, name: 1464824568
[INFO ] 2025-06-19 15:37:38.217  [HazelcastSourcePdkBaseNode_Source-Runner-6853a97351463b1f7959ad8e-sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388]-19-thread-1] HazelcastPdkBaseNode - Create PDK connector on node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] complete | Associate id: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183
[INFO ] 2025-06-19 15:37:38.304  [HazelcastTargetPdkDataNode_Target-Queue-Consumer-local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2]@task-任务 19-18-thread-1] HazelcastPdkBaseNode - Create PDK connector on node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] complete | Associate id: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183
[INFO ] 2025-06-19 15:37:38.314  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 15:37:38.315  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:37:38.315  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 15:37:38.484  [EMS-3-thread-1] PDK - ProxySubscriptionManager [syncSubscribeIds, allKeys [source#6853a7f951463b1f7959acc0, target#68341b6a23cbc765cf747e04, processId_zed_flow_engine]]
[INFO ] 2025-06-19 15:37:38.516  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 15:37:39.103  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:37:39.104  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 15:37:39.104  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 15:37:39.104  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 15:37:39.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 15:37:39.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 15:37:39.164  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 15:37:39.165  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.165  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:37:39.529  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 15:37:39.933  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:37:39.933  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[INFO ] 2025-06-19 15:41:17.858  [Thread-websocket-handle-message--2-thread-9] BaseEventHandler - Stop task from websocket event: {type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}
[INFO ] 2025-06-19 15:41:17.859  [Thread-websocket-handle-message--2-thread-9] TapdataTaskScheduler - Send stop task operation: 6853a97351463b1f7959ad8e
[INFO ] 2025-06-19 15:41:17.859  [Thread-websocket-handle-message--2-thread-9] ManagementWebsocketHandler - Processed message result WebSocketEvent{type='pipe', receiver='null', sender='zed_flow_engine', data=WebSocketEventResult{type='dataSyncResult', error='null', status='SUCCESS', result={type=dataSync, taskId=6853a97351463b1f7959ad8e, opType=stop, force=false}}, code='null'}.
[INFO ] 2025-06-19 15:41:17.860  [Stop-Task-Operation-Handler-6853a97351463b1f7959ad8e] TapdataTaskScheduler - Handled task operation: StopTaskOperation{taskId='6853a97351463b1f7959ad8e'} TaskOperation{, opType=STOP}
[TRACE] 2025-06-19 15:41:17.904  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 15:41:18.722  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:41:20.907  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.908  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:41:20.909  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:41:20.909  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:41:20.927  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.927  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:41:21.133  [Read_File_Appender_TAILER_Thread] job-file-log-6853a97351463b1f7959ad8e - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 18 ms 
[WARN ] 2025-06-19 15:41:21.585  [Internal Stop Task Scheduler[zed_flow_engine]] HazelcastTaskClient - The task is not running, status:  SUSPENDED [java.base/java.lang.Thread.getStackTrace(Thread.java:1619), io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.isRunning(HazelcastTaskClient.java:253), io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.errorOrStopTask(TapdataTaskScheduler.java:423), jdk.internal.reflect.GeneratedMethodAccessor61.invoke(Unknown Source), java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.base/java.lang.reflect.Method.invoke(Method.java:569), org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130), org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124), io.micrometer.observation.Observation.observe(Observation.java:498), org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124), org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85), org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54), java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539), java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305), java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305), java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136), java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635), java.base/java.lang.Thread.run(Thread.java:840)]
[INFO ] 2025-06-19 15:41:21.774  [6853a97351463b1f7959ad8e-TaskInspect-high-priority-checker] AbsInnerJob - exit

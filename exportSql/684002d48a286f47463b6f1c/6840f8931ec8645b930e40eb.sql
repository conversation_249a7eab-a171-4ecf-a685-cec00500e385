 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('1'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('1'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('2'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('2'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('3'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('3'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('4'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('4'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('5'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('5'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('6'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('6'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('7'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('7'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('8'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('8'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('9'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('9'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('10'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('10'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('11'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('11'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('12'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('12'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('13'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('13'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('14'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('14'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('15'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('15'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('16'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('16'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('17'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('17'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('18'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('18'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('19'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('19'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('20'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('20'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('21'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('21'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('22'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('22'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('23'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('23'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('24'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('24'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)
 INSERT INTO "public"."address_key_new" ("record_id", "long_key", "short_key", "soundex_key", "record_type", "region", "_no_pk_hash") VALUES(('25'::int4),('BFLMT                   '),('BDL MT '),('B600W300'),('U'),('HK'),(NULL)) ON CONFLICT() DO UPDATE SET "record_id"=('25'::int4), "long_key"=('BFLMT                   '), "short_key"=('BDL MT '), "soundex_key"=('B600W300'), "record_type"=('U'), "region"=('HK'), "_no_pk_hash"=(NULL)

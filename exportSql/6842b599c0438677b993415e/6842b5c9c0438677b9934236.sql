INSERT INTO "public"."SourceOfRegion" ("<PERSON>s_<PERSON>", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1     ','200 ','pgizvY6Pyd','2020-03-31T22:30:30','2002-12-14','2006-05-31T23:46:34') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1     ', "Create_Dtm"='200 ', "Update_By"='pgizvY6Pyd', "Update_Dtm"='2020-03-31T22:30:30'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('10    ','200 ','Cr390dNVYl','2012-03-02T18:28:44','2018-04-28','2018-05-19T06:41:28') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='10    ', "Create_Dtm"='200 ', "Update_By"='Cr390dNVYl', "Update_Dtm"='2012-03-02T18:28:44'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('100   ','200 ','OK5LGqPr2L','2024-02-09T13:55:40','2011-10-10','2016-11-14T13:24:30') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='100   ', "Create_Dtm"='200 ', "Update_By"='OK5LGqPr2L', "Update_Dtm"='2024-02-09T13:55:40'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1000  ','200 ','IVGZJ5TYsS','2001-02-12T02:50:27','2024-10-23','2009-05-15T15:24:09') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1000  ', "Create_Dtm"='200 ', "Update_By"='IVGZJ5TYsS', "Update_Dtm"='2001-02-12T02:50:27'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('10000 ','866 ','zl9Lps28y2','2008-01-19T23:10:22','2023-11-02','2008-12-17T08:36:48') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='10000 ', "Create_Dtm"='866 ', "Update_By"='zl9Lps28y2', "Update_Dtm"='2008-01-19T23:10:22'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1001  ','200 ','sxjMZcoQIm','2001-11-12T07:09:11','2013-12-14','2014-08-15T06:22:27') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1001  ', "Create_Dtm"='200 ', "Update_By"='sxjMZcoQIm', "Update_Dtm"='2001-11-12T07:09:11'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1002  ','200 ','mnnxzFnE4x','2021-09-18T15:55:32','2004-07-28','2014-05-11T18:57:08') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1002  ', "Create_Dtm"='200 ', "Update_By"='mnnxzFnE4x', "Update_Dtm"='2021-09-18T15:55:32'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1003  ','200 ','66HhsXaFoa','2002-10-21T08:45:33','2013-12-10','2011-05-18T04:49:06') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1003  ', "Create_Dtm"='200 ', "Update_By"='66HhsXaFoa', "Update_Dtm"='2002-10-21T08:45:33'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1004  ','25  ','itxwN1NPdc','2000-04-02T08:48:13','2002-03-19','2003-06-27T01:13:25') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1004  ', "Create_Dtm"='25  ', "Update_By"='itxwN1NPdc', "Update_Dtm"='2000-04-02T08:48:13'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1005  ','309 ','9zXaUzgXxK','2015-11-18T18:10:28','2011-01-14','2014-05-11T19:24:46') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1005  ', "Create_Dtm"='309 ', "Update_By"='9zXaUzgXxK', "Update_Dtm"='2015-11-18T18:10:28'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1006  ','916 ','gfttOPDnv8','2001-11-02T00:46:18','2015-10-18','2020-01-11T02:19:22') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1006  ', "Create_Dtm"='916 ', "Update_By"='gfttOPDnv8', "Update_Dtm"='2001-11-02T00:46:18'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1007  ','200 ','Q2ubX8uiA2','2007-04-05T18:48:56','2004-04-14','2001-06-26T04:34:07') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1007  ', "Create_Dtm"='200 ', "Update_By"='Q2ubX8uiA2', "Update_Dtm"='2007-04-05T18:48:56'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1008  ','207 ','8okmEbKLxG','2017-07-04T05:36:27','2006-10-05','2022-07-14T23:14:37') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1008  ', "Create_Dtm"='207 ', "Update_By"='8okmEbKLxG', "Update_Dtm"='2017-07-04T05:36:27'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1009  ','200 ','htySmogBIs','2024-07-02T16:13:29','2005-06-12','2006-01-16T23:28:16') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1009  ', "Create_Dtm"='200 ', "Update_By"='htySmogBIs', "Update_Dtm"='2024-07-02T16:13:29'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('101   ','565 ','uo5K8KbbAR','2009-10-11T23:03:09','2010-06-22','2002-04-06T15:44:41') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='101   ', "Create_Dtm"='565 ', "Update_By"='uo5K8KbbAR', "Update_Dtm"='2009-10-11T23:03:09'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1010  ','200 ','k6tJn0UarS','2007-06-12T06:17:08','2006-09-21','2014-08-16T13:41:23') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1010  ', "Create_Dtm"='200 ', "Update_By"='k6tJn0UarS', "Update_Dtm"='2007-06-12T06:17:08'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1011  ','403 ','ZC7wdB9BSH','2014-04-18T23:51:28','2015-06-09','2021-08-16T03:36:37') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1011  ', "Create_Dtm"='403 ', "Update_By"='ZC7wdB9BSH', "Update_Dtm"='2014-04-18T23:51:28'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1012  ','200 ','B2NsAyGqpH','2001-08-11T01:41:04','2011-05-21','2009-10-26T21:57:29') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1012  ', "Create_Dtm"='200 ', "Update_By"='B2NsAyGqpH', "Update_Dtm"='2001-08-11T01:41:04'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1013  ','200 ','WLuRBdNAx5','2018-06-17T11:12:24','2024-07-24','2024-02-18T05:56:07') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1013  ', "Create_Dtm"='200 ', "Update_By"='WLuRBdNAx5', "Update_Dtm"='2018-06-17T11:12:24'
INSERT INTO "public"."SourceOfRegion" ("Herbs_ID", "Region_ID", "Create_By", "Create_Dtm", "Update_By", "Update_Dtm") VALUES('1014  ','200 ','9h3YlZbPev','2012-05-30T18:16:31','2008-08-26','2000-04-05T21:41:52') ON CONFLICT("Herbs_ID", "Region_ID") DO UPDATE SET "Create_By"='1014  ', "Create_Dtm"='200 ', "Update_By"='9h3YlZbPev', "Update_Dtm"='2012-05-30T18:16:31'
